import React from "react";
import { ChevronDown } from "lucide-react";
import { Button } from "../common/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "../common/tooltip";

interface TooltipButtonProps {
  icon: React.ReactNode; // 按钮的图标
  label: string; // 按钮的文本
  onClick: () => void; // 点击按钮时调用的事件
  disabled?: boolean; // 是否禁用按钮
  size?: "icon" | "sm" | "default" | "lg"; // 按钮大小
  variant?:
    | "ghost"
    | "link"
    | "default"
    | "destructive"
    | "outline"
    | "secondary"; // 按钮样式类型
  showDropdownArrow?: boolean; // 是否显示下拉箭头
  iconOnly?: boolean; // 是否只显示图标
}

export const TooltipButton = ({
  icon,
  label,
  onClick,
  disabled = false,
  size = "sm",
  variant = "ghost",
  showDropdownArrow = true,
  iconOnly = false,
}: TooltipButtonProps) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={variant}
          size={size}
          disabled={disabled} // 如果节点未选中，按钮会禁用
          className="button-small" // 按钮样式
          onClick={onClick} // 点击事件
        >
          {icon && <span className="icon-small">{icon}</span>}
          {!iconOnly && <span className="text-small">{label}</span>}
          {!iconOnly && showDropdownArrow && (
            <div className="margin-left-medium">
              <ChevronDown className="dropdown-arrow" />
            </div>
          )}
        </Button>
      </TooltipTrigger>
      <TooltipContent>{label}</TooltipContent> {/* 显示 Tooltip 内容 */}
    </Tooltip>
  );
};
