import React, { useState, useRef } from "react";
import { ChevronUp, ChevronDown } from "lucide-react";
import { Button } from "../common/button";
import { Separator } from "../common/separator";
import { Popover, PopoverContent, PopoverTrigger } from "../common/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../common/tooltip";
import { useMindMapStore } from "../../store/mindMapStore";
import { TooltipButton } from "./TooltipButton";
import { style_icons } from "../../config/constants";

interface StyleMenuProps {
  isNodeSelected: boolean;
  currentNode: any;
}

const borderWidths = [0, 1, 2, 3, 4, 5]; // 0 表示"无"

export const StyleMenu = ({ isNodeSelected, currentNode }: StyleMenuProps) => {
  const [borderWidthOpen, setBorderWidthOpen] = useState(false);
  const borderWidthButtonRef = useRef<HTMLButtonElement>(null);

  // 使用全局状态管理
  const { selectedNode, updateBorderWidth } = useMindMapStore();

  return (
    <div className="menu-container">
      <TooltipProvider delayDuration={0}>
        {style_icons.map(
          ({ icon, label, onClick, showDropdownArrow, iconOnly }, index) => (
            <React.Fragment key={label}>
              {/* 如果前一个图标有 addSeparator 属性，则在当前图标前显示分隔符 */}
              {index > 0 && style_icons[index - 1].addSeparator && (
                <Separator orientation="vertical" className="separator" />
              )}
              <div className="button-container">
                {/* 如果是边框宽度，显示边框宽度选择器 */}
                {label === "边框宽度" ? (
                  <div className="flex-center-gap-small">
                    <Popover
                      open={borderWidthOpen}
                      onOpenChange={(open) => {
                        setBorderWidthOpen(open);
                      }}
                    >
                      <Tooltip>
                        <TooltipTrigger
                          asChild
                          onFocus={(e) => {
                            // 阻止焦点触发 tooltip
                            e.preventDefault();
                            e.stopPropagation();
                          }}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              ref={borderWidthButtonRef}
                              variant="ghost"
                              size="sm"
                              disabled={!isNodeSelected}
                              className={`button-small ${borderWidthOpen ? "button-active" : ""}`}
                            >
                              {icon && (
                                <span className="icon-small">
                                  <img src={icon} alt={label} />
                                </span>
                              )}
                              {!iconOnly && (
                                <span className="text-small">{label}</span>
                              )}
                              <div className="margin-left-medium">
                                {borderWidthOpen ? (
                                  <ChevronUp className="dropdown-arrow" />
                                ) : (
                                  <ChevronDown className="dropdown-arrow" />
                                )}
                              </div>
                            </Button>
                          </PopoverTrigger>
                        </TooltipTrigger>
                        <TooltipContent>边框宽度</TooltipContent>
                      </Tooltip>
                      <PopoverContent className="popover-content-border">
                        <div className="border-width-list">
                          {borderWidths.map((width) => {
                            const isSelected =
                              currentNode?.style.borderWidth === width;
                            const displayText =
                              width === 0 ? "无" : `${width}px`;

                            return (
                              <Button
                                key={width}
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  if (selectedNode) {
                                    updateBorderWidth(selectedNode.id, width);
                                  }
                                  setBorderWidthOpen(false);
                                }}
                                className={`border-width-item ${isSelected ? "border-width-selected" : ""}`}
                              >
                                <svg
                                  className={`check-icon ${isSelected ? "check-icon-visible" : "check-icon-hidden"}`}
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <polyline points="20,6 9,17 4,12"></polyline>
                                </svg>
                                <span className="border-width-text">
                                  {displayText}
                                </span>
                              </Button>
                            );
                          })}
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                ) : (
                  <TooltipButton
                    icon={icon ? <img src={icon} alt={label} /> : null} // 只有当icon不为空时才显示图标
                    label={label} // 文本
                    onClick={onClick} // 传入按钮点击时调用的函数
                    disabled={!isNodeSelected} // 如果没有选中节点，按钮禁用
                    showDropdownArrow={showDropdownArrow} // 是否显示下拉箭头
                    iconOnly={iconOnly} // 是否只显示图标
                  />
                )}
              </div>
            </React.Fragment>
          )
        )}
      </TooltipProvider>
    </div>
  );
};
