import { useCallback, useRef } from "react";
import MindMapNode from "./MindMapNode";
import ConnectionRenderer from "./ConnectionRenderer";
import type {
  MindMapData,
  SelectedNode,
  MindMapNode as NodeType,
} from "../types/mindmap";
import { useCanvasDrag } from "../hooks/useCanvasDrag";
import { useNodeDimensions } from "../hooks/useNodeDimensions";
import { useSVGBounds } from "../hooks/useSVGBounds";
import { useChildNodeAdjustment } from "../hooks/useChildNodeAdjustment";
import "./MindMapCanvas.css";

interface MindMapCanvasProps {
  mindMapData: MindMapData;
  selectedNode: SelectedNode | null;
  onNodeSelect: (node: SelectedNode | null) => void;
  onNodeUpdate: (nodeId: string, updates: Partial<NodeType>) => void;
  onAddChild: (parentId: string) => void;
  onToggleCollapse: (nodeId: string) => void;
  onContextMenu: (
    e: React.MouseEvent,
    nodeId: string,
    nodeLevel: number
  ) => void;
}

export default function MindMapCanvas({
  mindMapData,
  selectedNode,
  onNodeSelect,
  onNodeUpdate,
  onAddChild,
  onToggleCollapse,
  onContextMenu,
}: MindMapCanvasProps) {
  const canvasRef = useRef<HTMLDivElement>(null);

  // 使用自定义 Hooks
  const { canvasOffset, handleMouseDown, cursor } = useCanvasDrag();
  const { nodeDimensions, updateNodeDimensions, getNodeDimensions } =
    useNodeDimensions();
  const { svgBounds } = useSVGBounds(mindMapData);

  const {
    handleNodeDimensionUpdate: baseHandleNodeDimensionUpdate,
    handleAddChildWithDimensions: baseHandleAddChildWithDimensions,
    adjustNewChildPosition,
  } = useChildNodeAdjustment({
    mindMapData,
    nodeDimensions,
    onNodeUpdate,
  });

  // 处理节点尺寸更新（包装 updateNodeDimensions）
  const handleNodeDimensionUpdate = useCallback(
    (
      nodeId: string,
      dimensions: { width: number; height: number },
      isFromEditing: boolean = false
    ) => {
      updateNodeDimensions(nodeId, dimensions, isFromEditing);
      baseHandleNodeDimensionUpdate(nodeId, dimensions, isFromEditing);
    },
    [updateNodeDimensions, baseHandleNodeDimensionUpdate]
  );

  // 处理添加子节点（包装 onAddChild）
  const handleAddChildWithDimensions = useCallback(
    (parentId: string) => {
      baseHandleAddChildWithDimensions(parentId, onAddChild);
    },
    [baseHandleAddChildWithDimensions, onAddChild]
  );

  const handleCanvasClick = (e: React.MouseEvent) => {
    // 检查是否点击了节点本身
    const isNodeClick = (e.target as HTMLElement).closest(".mindmap-node");

    // 如果没有点击节点，则取消选中
    if (!isNodeClick) {
      onNodeSelect(null);
    }
  };

  return (
    <div
      ref={canvasRef}
      className="mindmap-canvas"
      onClick={handleCanvasClick}
      onMouseDown={handleMouseDown}
      style={{
        cursor,
      }}
    >
      {/* SVG 连接线层 */}
      <ConnectionRenderer
        mindMapData={mindMapData}
        nodeDimensions={nodeDimensions}
        svgBounds={svgBounds}
        canvasOffset={canvasOffset}
        getNodeDimensions={getNodeDimensions}
      />

      {/* 节点层 */}
      <div
        className="nodes-layer"
        style={{
          width: svgBounds.width,
          height: svgBounds.height,
          transform: `translate(${-svgBounds.minX + canvasOffset.x}px, ${-svgBounds.minY + canvasOffset.y}px)`,
        }}
      >
        {Object.values(mindMapData.nodes)
          .filter((node) => {
            // 如果是根节点，总是显示
            if (node.level === 1) return true;

            // 检查父节点是否被收缩
            if (node.parentId) {
              const parentNode = mindMapData.nodes[node.parentId];
              return !parentNode?.collapsed;
            }

            return true;
          })
          .map((node) => (
            <MindMapNode
              key={node.id}
              node={node}
              isSelected={selectedNode?.id === node.id}
              onSelect={() => onNodeSelect({ id: node.id, level: node.level })}
              onUpdate={(updates) => onNodeUpdate(node.id, updates)}
              onAddChild={() => handleAddChildWithDimensions(node.id)}
              onToggleCollapse={() => onToggleCollapse(node.id)}
              onContextMenu={(e) => onContextMenu(e, node.id, node.level)}
              canAddChild={node.level < 3}
              onDimensionUpdate={handleNodeDimensionUpdate}
              onAdjustChildPosition={adjustNewChildPosition}
            />
          ))}
      </div>
    </div>
  );
}
