import { useCallback, useRef, useState, useEffect } from "react";
import MindMapNode from "./MindMapNode";
import type {
  MindMapData,
  SelectedNode,
  MindMapNode as NodeType,
} from "../types/mindmap";
import "./MindMapCanvas.css";

interface MindMapCanvasProps {
  mindMapData: MindMapData;
  selectedNode: SelectedNode | null;
  onNodeSelect: (node: SelectedNode | null) => void;
  onNodeUpdate: (nodeId: string, updates: Partial<NodeType>) => void;
  onAddChild: (parentId: string) => void;
  onToggleCollapse: (nodeId: string) => void;
  onContextMenu: (
    e: React.MouseEvent,
    nodeId: string,
    nodeLevel: number
  ) => void;
}

export default function MindMapCanvas({
  mindMapData,
  selectedNode,
  onNodeSelect,
  onNodeUpdate,
  onAddChild,
  onToggleCollapse,
  onContextMenu,
}: MindMapCanvasProps) {
  const canvasRef = useRef<HTMLDivElement>(null);

  // 存储节点的实际尺寸信息
  const [nodeDimensions, setNodeDimensions] = useState<
    Record<string, { width: number; height: number }>
  >({});

  // 防止无限循环的标志
  const isAdjustingPositions = useRef(false);

  // 拖拽相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
  const dragStartOffset = useRef({ x: 0, y: 0 });

  // 调整子节点位置（在曲线布局基础上进行微调）
  const adjustChildrenPositions = useCallback(
    (parentId: string, oldWidth?: number, newWidth?: number) => {
      if (isAdjustingPositions.current) return;

      const parent = mindMapData.nodes[parentId];
      if (!parent || parent.children.length === 0) return;

      // 如果提供了宽度参数，计算宽度差异
      let widthDiff = 0;
      if (oldWidth !== undefined && newWidth !== undefined) {
        widthDiff = newWidth - oldWidth;
        if (Math.abs(widthDiff) < 1) return; // 忽略微小的变化
      }

      isAdjustingPositions.current = true;

      // 获取父节点的实际尺寸
      const parentDimensions = nodeDimensions[parentId];
      if (!parentDimensions) {
        isAdjustingPositions.current = false;
        return;
      }

      // 递归调整所有后代节点的X位置
      const adjustAllDescendants = (
        nodeId: string,
        currentWidthDiff: number
      ) => {
        const node = mindMapData.nodes[nodeId];
        if (!node) return;

        node.children.forEach((childId) => {
          const child = mindMapData.nodes[childId];
          if (child) {
            // 计算子节点应该紧贴父节点右边框的新X位置
            const newX = node.position.x + (nodeDimensions[nodeId]?.width || 0);

            // 如果有宽度变化，在新位置基础上应用宽度差异
            const finalX =
              currentWidthDiff !== 0 ? newX + currentWidthDiff : newX;

            onNodeUpdate(childId, {
              position: {
                ...child.position,
                x: finalX,
              },
            });

            // 递归调整子节点的子节点
            adjustAllDescendants(childId, currentWidthDiff);
          }
        });
      };

      adjustAllDescendants(parentId, widthDiff);

      // 延迟重置标志，避免阻塞后续的正常更新
      setTimeout(() => {
        isAdjustingPositions.current = false;
      }, 100);
    },
    [mindMapData.nodes, onNodeUpdate, nodeDimensions]
  );

  // 处理节点尺寸更新
  const handleNodeDimensionUpdate = useCallback(
    (
      nodeId: string,
      dimensions: { width: number; height: number },
      isFromEditing: boolean = false
    ) => {
      setNodeDimensions((prev) => {
        const prevDimensions = prev[nodeId];
        const newDimensions = {
          ...prev,
          [nodeId]: dimensions,
        };

        // 只有在编辑模式下且节点宽度发生变化时，才调整子节点位置
        // 样式变化导致的尺寸更新不应该触发子节点位置调整
        if (
          !isAdjustingPositions.current &&
          isFromEditing &&
          prevDimensions &&
          Math.abs(prevDimensions.width - dimensions.width) > 1
        ) {
          const node = mindMapData.nodes[nodeId];
          if (node && node.children.length > 0) {
            // 使用 setTimeout 确保状态更新完成后再调整位置
            setTimeout(() => {
              adjustChildrenPositions(
                nodeId,
                prevDimensions.width,
                dimensions.width
              );
            }, 0);
          }
        }

        return newDimensions;
      });
    },
    [mindMapData.nodes, adjustChildrenPositions]
  );

  // 处理添加子节点（添加后立即调整位置以保持对齐）
  const handleAddChildWithDimensions = useCallback(
    (parentId: string) => {
      // 添加子节点
      onAddChild(parentId);

      // 延迟调整新子节点的位置，确保与已有子节点对齐
      setTimeout(() => {
        adjustChildrenPositions(parentId);
      }, 50); // 短暂延迟，确保节点已经添加到DOM中
    },
    [onAddChild, adjustChildrenPositions]
  );

  // 调整新添加子节点的位置（基于父节点实际宽度）
  const adjustNewChildPosition = useCallback(
    (parentId: string) => {
      // 直接调用 adjustChildrenPositions 来处理位置调整
      // 不传递宽度参数，让它根据当前实际尺寸重新计算位置
      adjustChildrenPositions(parentId);
    },
    [adjustChildrenPositions]
  );

  // 计算连接线路径
  const getConnectionPath = useCallback(
    (parent: NodeType, child: NodeType): string => {
      // 动态计算节点尺寸（优先使用实际尺寸，否则基于文本字符数和样式计算）
      const getNodeDimensions = (node: NodeType) => {
        // 优先使用存储的实际尺寸
        const actualDimensions = nodeDimensions[node.id];
        if (actualDimensions) {
          return actualDimensions;
        }

        // 如果没有实际尺寸，则动态计算
        const borderWidth = node.style.borderWidth || 1; // 获取节点的边框宽度
        const textLength = node.text.length; // 动态获取文本字符数

        switch (node.level) {
          case 1: {
            // padding: 12px 16px, fontSize: 20px
            const level1Width = borderWidth * 2 + 16 * 2 + textLength * 20;
            const level1Height = borderWidth * 2 + 12 * 2 + 20;
            return { width: level1Width, height: level1Height };
          }

          case 2: {
            // padding: 10px 14px, fontSize: 14px
            const level2Width = borderWidth * 2 + 14 * 2 + textLength * 14;
            const level2Height = borderWidth * 2 + 10 * 2 + 14;
            return { width: level2Width, height: level2Height };
          }

          case 3: {
            // padding: 8px 12px, fontSize: 14px,

            const level3Width = borderWidth * 2 + 12 * 2 + textLength * 14;
            const level3Height = borderWidth * 2 + 8 * 2 + 14;
            return { width: level3Width, height: level3Height };
          }

          default: {
            const defaultPaddingH = 14;
            const defaultPaddingV = 10;
            const defaultFontSize = 14;

            const defaultWidth =
              borderWidth * 2 +
              defaultPaddingH * 2 +
              textLength * defaultFontSize;
            const defaultHeight =
              borderWidth * 2 + defaultPaddingV * 2 + defaultFontSize;
            return { width: defaultWidth, height: defaultHeight };
          }
        }
      };

      const parentDim = getNodeDimensions(parent);
      const childDim = getNodeDimensions(child);

      // 计算父节点右边框中心点作为起点
      const startX = parent.position.x + parentDim.width;
      const startY = parent.position.y + parentDim.height / 2;

      // 计算子节点左边框中心点作为终点（紧贴节点边框）
      const endX = child.position.x;
      const endY = child.position.y + childDim.height / 2;

      // 根据父节点层级和子节点个数决定连接方式
      const childrenCount = parent.children.length;
      const childIndex = parent.children.indexOf(child.id);

      let useStraightLine = false;

      if (parent.level === 1) {
        // level1节点：永远使用贝塞尔曲线连接
        useStraightLine = false;
      } else if (parent.level === 2) {
        // level2节点：应用特殊的连接规则
        if (childrenCount % 2 === 1) {
          // 奇数个子节点：中间那个子节点使用直线连接
          const middleIndex = Math.floor(childrenCount / 2);
          useStraightLine = childIndex === middleIndex;
        } else {
          // 偶数个子节点：所有子节点都使用贝塞尔曲线连接
          useStraightLine = false;
        }
      } else {
        // level3及以上节点：所有连接都使用贝塞尔曲线
        useStraightLine = false;
      }

      if (useStraightLine) {
        // 使用直线连接
        return `M ${startX} ${startY} L ${endX} ${endY}`;
      } else {
        // 使用贝塞尔曲线连接
        const controlX1 = startX + (endX - startX) * 0.3;
        const controlY1 = startY;
        const controlX2 = startX + (endX - startX) * 0.7;
        const controlY2 = endY;

        return `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`;
      }
    },
    [nodeDimensions]
  );

  // 渲染所有连接线
  const renderConnections = () => {
    const connections: JSX.Element[] = [];

    for (const node of Object.values(mindMapData.nodes)) {
      // 如果节点被收缩，不渲染到其子节点的连接线
      if (node.collapsed) {
        continue;
      }

      for (const childId of node.children) {
        const childNode = mindMapData.nodes[childId];
        if (childNode) {
          const path = getConnectionPath(node, childNode);
          connections.push(
            <path
              key={`${node.id}-${childId}`}
              d={path}
              stroke="#666"
              strokeWidth="2"
              fill="none"
              className="connection-line"
            />
          );
        }
      }
    }

    return connections;
  };

  // 计算SVG视窗大小
  const getSVGBounds = () => {
    const nodes = Object.values(mindMapData.nodes);
    if (nodes.length === 0)
      return { width: 1000, height: 800, minX: 0, minY: 0 };

    let minX = Number.POSITIVE_INFINITY;
    let minY = Number.POSITIVE_INFINITY;
    let maxX = Number.NEGATIVE_INFINITY;
    let maxY = Number.NEGATIVE_INFINITY;

    for (const node of nodes) {
      minX = Math.min(minX, node.position.x);
      minY = Math.min(minY, node.position.y);
      maxX = Math.max(maxX, node.position.x + 200); // 节点宽度
      maxY = Math.max(maxY, node.position.y + 40); // 节点高度
    }

    return {
      width: Math.max(1000, maxX - minX + 500),
      height: Math.max(800, maxY - minY + 500),
      minX: Math.min(0, minX - 100),
      minY: Math.min(0, minY - 100),
    };
  };

  const svgBounds = getSVGBounds();

  // 处理鼠标按下事件（开始拖拽）
  const handleMouseDown = (e: React.MouseEvent) => {
    // 检查是否点击了可编辑的元素（输入框、按钮等）
    const target = e.target as HTMLElement;
    // console.log(target);

    const isEditableElement =
      target.tagName === "INPUT" ||
      target.tagName === "BUTTON" ||
      target.closest("button") ||
      target.closest(".color-picker") ||
      target.closest(".format-toolbar");

    // 如果不是可编辑元素，则开始拖拽
    if (!isEditableElement) {
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
      dragStartOffset.current = { ...canvasOffset };
      e.preventDefault();
    }
  };

  // 处理鼠标移动事件（拖拽中）
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging) {
        const deltaX = e.clientX - dragStart.x;
        const deltaY = e.clientY - dragStart.y;

        setCanvasOffset({
          x: dragStartOffset.current.x + deltaX,
          y: dragStartOffset.current.y + deltaY,
        });
      }
    },
    [isDragging, dragStart.x, dragStart.y]
  );

  // 处理鼠标松开事件（结束拖拽）
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const handleCanvasClick = (e: React.MouseEvent) => {
    // 检查是否点击了思维导图区域之外的空白区域
    const isOutsideMindMap = e.target === e.currentTarget;
    // 只有点击画布外部空白区域才取消选中
    if (isOutsideMindMap) {
      onNodeSelect(null);
    }
  };

  return (
    <div
      ref={canvasRef}
      className="mindmap-canvas"
      onClick={handleCanvasClick}
      onMouseDown={handleMouseDown}
      style={{
        cursor: isDragging ? "grabbing" : "grab",
      }}
    >
      {/* SVG 连接线层 */}
      <svg
        className="connections-layer"
        width="100%"
        height="100%"
        style={{
          transform: `translate(${-svgBounds.minX + canvasOffset.x}px, ${-svgBounds.minY + canvasOffset.y}px)`,
        }}
      >
        {renderConnections()}
      </svg>

      {/* 节点层 */}
      <div
        className="nodes-layer"
        style={{
          width: svgBounds.width,
          height: svgBounds.height,
          transform: `translate(${-svgBounds.minX + canvasOffset.x}px, ${-svgBounds.minY + canvasOffset.y}px)`,
        }}
      >
        {Object.values(mindMapData.nodes)
          .filter((node) => {
            // 如果是根节点，总是显示
            if (node.level === 1) return true;

            // 检查父节点是否被收缩
            if (node.parentId) {
              const parentNode = mindMapData.nodes[node.parentId];
              return !parentNode?.collapsed;
            }

            return true;
          })
          .map((node) => (
            <MindMapNode
              key={node.id}
              node={node}
              isSelected={selectedNode?.id === node.id}
              onSelect={() => onNodeSelect({ id: node.id, level: node.level })}
              onUpdate={(updates) => onNodeUpdate(node.id, updates)}
              onAddChild={() => handleAddChildWithDimensions(node.id)}
              onToggleCollapse={() => onToggleCollapse(node.id)}
              onContextMenu={(e) => onContextMenu(e, node.id, node.level)}
              canAddChild={node.level < 3}
              onDimensionUpdate={handleNodeDimensionUpdate}
              onAdjustChildPosition={adjustNewChildPosition}
            />
          ))}
      </div>
    </div>
  );
}
