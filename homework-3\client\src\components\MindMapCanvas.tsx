import { useCallback, useRef } from "react";
import MindMapNode from "./MindMapNode";
import type {
  MindMapData,
  SelectedNode,
  MindMapNode as NodeType,
} from "../types/mindmap";
import { useCanvasDrag } from "../hooks/useCanvasDrag";
import { useNodeDimensions } from "../hooks/useNodeDimensions";
import { useSVGBounds } from "../hooks/useSVGBounds";
import "./MindMapCanvas.css";

interface MindMapCanvasProps {
  mindMapData: MindMapData;
  selectedNode: SelectedNode | null;
  onNodeSelect: (node: SelectedNode | null) => void;
  onNodeUpdate: (nodeId: string, updates: Partial<NodeType>) => void;
  onAddChild: (parentId: string) => void;
  onToggleCollapse: (nodeId: string) => void;
  onContextMenu: (
    e: React.MouseEvent,
    nodeId: string,
    nodeLevel: number
  ) => void;
}

export default function MindMapCanvas({
  mindMapData,
  selectedNode,
  onNodeSelect,
  onNodeUpdate,
  onAddChild,
  onToggleCollapse,
  onContextMenu,
}: MindMapCanvasProps) {
  const canvasRef = useRef<HTMLDivElement>(null);

  // 防止无限循环的标志
  const isAdjustingPositions = useRef(false);

  // 使用自定义 Hooks
  const { canvasOffset, handleMouseDown, cursor } = useCanvasDrag();
  const { nodeDimensions, updateNodeDimensions, getNodeDimensions } =
    useNodeDimensions();
  const { svgBounds } = useSVGBounds(mindMapData);

  // 调整子节点位置（在曲线布局基础上进行微调）
  const adjustChildrenPositions = useCallback(
    (parentId: string, oldWidth?: number, newWidth?: number) => {
      if (isAdjustingPositions.current) return;

      const parent = mindMapData.nodes[parentId];
      if (!parent || parent.children.length === 0) return;

      // 如果提供了宽度参数，计算宽度差异
      let widthDiff = 0;
      if (oldWidth !== undefined && newWidth !== undefined) {
        widthDiff = newWidth - oldWidth;
        if (Math.abs(widthDiff) < 1) return; // 忽略微小的变化
      }

      isAdjustingPositions.current = true;

      // 获取父节点的实际尺寸
      const parentDimensions = nodeDimensions[parentId];
      if (!parentDimensions) {
        isAdjustingPositions.current = false;
        return;
      }

      // 递归调整所有后代节点的X位置
      const adjustAllDescendants = (
        nodeId: string,
        currentWidthDiff: number
      ) => {
        const node = mindMapData.nodes[nodeId];
        if (!node) return;

        node.children.forEach((childId) => {
          const child = mindMapData.nodes[childId];
          if (child) {
            // 计算子节点应该紧贴父节点右边框的新X位置
            const newX = node.position.x + (nodeDimensions[nodeId]?.width || 0);

            // 如果有宽度变化，在新位置基础上应用宽度差异
            const finalX =
              currentWidthDiff !== 0 ? newX + currentWidthDiff : newX;

            onNodeUpdate(childId, {
              position: {
                ...child.position,
                x: finalX,
              },
            });

            // 递归调整子节点的子节点
            adjustAllDescendants(childId, currentWidthDiff);
          }
        });
      };

      adjustAllDescendants(parentId, widthDiff);

      // 延迟重置标志，避免阻塞后续的正常更新
      setTimeout(() => {
        isAdjustingPositions.current = false;
      }, 100);
    },
    [mindMapData.nodes, onNodeUpdate, nodeDimensions]
  );

  // 处理节点尺寸更新
  const handleNodeDimensionUpdate = useCallback(
    (
      nodeId: string,
      dimensions: { width: number; height: number },
      isFromEditing: boolean = false
    ) => {
      const prevDimensions = nodeDimensions[nodeId];

      updateNodeDimensions(nodeId, dimensions, isFromEditing);

      // 只有在编辑模式下且节点宽度发生变化时，才调整子节点位置
      if (
        !isAdjustingPositions.current &&
        isFromEditing &&
        prevDimensions &&
        Math.abs(prevDimensions.width - dimensions.width) > 1
      ) {
        const node = mindMapData.nodes[nodeId];
        if (node && node.children.length > 0) {
          // 使用 setTimeout 确保状态更新完成后再调整位置
          setTimeout(() => {
            adjustChildrenPositions(
              nodeId,
              prevDimensions.width,
              dimensions.width
            );
          }, 0);
        }
      }
    },
    [
      mindMapData.nodes,
      adjustChildrenPositions,
      nodeDimensions,
      updateNodeDimensions,
    ]
  );

  // 处理添加子节点（添加后立即调整位置以保持对齐）
  const handleAddChildWithDimensions = useCallback(
    (parentId: string) => {
      // 添加子节点
      onAddChild(parentId);

      // 延迟调整新子节点的位置，确保与已有子节点对齐
      setTimeout(() => {
        adjustChildrenPositions(parentId);
      }, 50); // 短暂延迟，确保节点已经添加到DOM中
    },
    [onAddChild, adjustChildrenPositions]
  );

  // 调整新添加子节点的位置（基于父节点实际宽度）
  const adjustNewChildPosition = useCallback(
    (parentId: string) => {
      // 直接调用 adjustChildrenPositions 来处理位置调整
      // 不传递宽度参数，让它根据当前实际尺寸重新计算位置
      adjustChildrenPositions(parentId);
    },
    [adjustChildrenPositions]
  );

  // 计算连接线路径
  const getConnectionPath = useCallback(
    (parent: NodeType, child: NodeType): string => {
      const parentDim = getNodeDimensions(parent);
      const childDim = getNodeDimensions(child);

      // 计算父节点右边框中心点作为起点
      const startX = parent.position.x + parentDim.width;
      const startY = parent.position.y + parentDim.height / 2;

      // 计算子节点左边框中心点作为终点（紧贴节点边框）
      const endX = child.position.x;
      const endY = child.position.y + childDim.height / 2;

      // 根据父节点层级和子节点个数决定连接方式
      const childrenCount = parent.children.length;
      const childIndex = parent.children.indexOf(child.id);

      let useStraightLine = false;

      if (parent.level === 1) {
        // level1节点：永远使用贝塞尔曲线连接
        useStraightLine = false;
      } else if (parent.level === 2) {
        // level2节点：应用特殊的连接规则
        if (childrenCount % 2 === 1) {
          // 奇数个子节点：中间那个子节点使用直线连接
          const middleIndex = Math.floor(childrenCount / 2);
          useStraightLine = childIndex === middleIndex;
        } else {
          // 偶数个子节点：所有子节点都使用贝塞尔曲线连接
          useStraightLine = false;
        }
      } else {
        // level3及以上节点：所有连接都使用贝塞尔曲线
        useStraightLine = false;
      }

      if (useStraightLine) {
        // 使用直线连接
        return `M ${startX} ${startY} L ${endX} ${endY}`;
      } else {
        // 使用贝塞尔曲线连接
        const controlX1 = startX + (endX - startX) * 0.3;
        const controlY1 = startY;
        const controlX2 = startX + (endX - startX) * 0.7;
        const controlY2 = endY;

        return `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`;
      }
    },
    [getNodeDimensions]
  );

  // 渲染所有连接线
  const renderConnections = () => {
    const connections: JSX.Element[] = [];

    for (const node of Object.values(mindMapData.nodes)) {
      // 如果节点被收缩，不渲染到其子节点的连接线
      if (node.collapsed) {
        continue;
      }

      for (const childId of node.children) {
        const childNode = mindMapData.nodes[childId];
        if (childNode) {
          const path = getConnectionPath(node, childNode);
          connections.push(
            <path
              key={`${node.id}-${childId}`}
              d={path}
              stroke="#666"
              strokeWidth="2"
              fill="none"
              className="connection-line"
            />
          );
        }
      }
    }

    return connections;
  };

  const handleCanvasClick = (e: React.MouseEvent) => {
    // 检查是否点击了节点本身
    const isNodeClick = (e.target as HTMLElement).closest(".mindmap-node");

    // 如果没有点击节点，则取消选中
    if (!isNodeClick) {
      onNodeSelect(null);
    }
  };

  return (
    <div
      ref={canvasRef}
      className="mindmap-canvas"
      onClick={handleCanvasClick}
      onMouseDown={handleMouseDown}
      style={{
        cursor,
      }}
    >
      {/* SVG 连接线层 */}
      <svg
        className="connections-layer"
        width="100%"
        height="100%"
        style={{
          transform: `translate(${-svgBounds.minX + canvasOffset.x}px, ${-svgBounds.minY + canvasOffset.y}px)`,
        }}
      >
        {renderConnections()}
      </svg>

      {/* 节点层 */}
      <div
        className="nodes-layer"
        style={{
          width: svgBounds.width,
          height: svgBounds.height,
          transform: `translate(${-svgBounds.minX + canvasOffset.x}px, ${-svgBounds.minY + canvasOffset.y}px)`,
        }}
      >
        {Object.values(mindMapData.nodes)
          .filter((node) => {
            // 如果是根节点，总是显示
            if (node.level === 1) return true;

            // 检查父节点是否被收缩
            if (node.parentId) {
              const parentNode = mindMapData.nodes[node.parentId];
              return !parentNode?.collapsed;
            }

            return true;
          })
          .map((node) => (
            <MindMapNode
              key={node.id}
              node={node}
              isSelected={selectedNode?.id === node.id}
              onSelect={() => onNodeSelect({ id: node.id, level: node.level })}
              onUpdate={(updates) => onNodeUpdate(node.id, updates)}
              onAddChild={() => handleAddChildWithDimensions(node.id)}
              onToggleCollapse={() => onToggleCollapse(node.id)}
              onContextMenu={(e) => onContextMenu(e, node.id, node.level)}
              canAddChild={node.level < 3}
              onDimensionUpdate={handleNodeDimensionUpdate}
              onAdjustChildPosition={adjustNewChildPosition}
            />
          ))}
      </div>
    </div>
  );
}
