import { useState, useRef, useCallback, useEffect } from "react";

interface CanvasOffset {
  x: number;
  y: number;
}

interface DragStart {
  x: number;
  y: number;
}

interface UseCanvasDragReturn {
  isDragging: boolean;
  canvasOffset: CanvasOffset;
  handleMouseDown: (e: React.MouseEvent) => void;
  cursor: string;
}

export function useCanvasDrag(): UseCanvasDragReturn {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<DragStart>({ x: 0, y: 0 });
  const [canvasOffset, setCanvasOffset] = useState<CanvasOffset>({ x: 0, y: 0 });
  const dragStartOffset = useRef<CanvasOffset>({ x: 0, y: 0 });

  // 处理鼠标移动事件（拖拽中）
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging) {
        const deltaX = e.clientX - dragStart.x;
        const deltaY = e.clientY - dragStart.y;

        setCanvasOffset({
          x: dragStartOffset.current.x + deltaX,
          y: dragStartOffset.current.y + deltaY,
        });
      }
    },
    [isDragging, dragStart.x, dragStart.y]
  );

  // 处理鼠标松开事件（结束拖拽）
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 处理鼠标按下事件（开始拖拽）
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    // 检查是否点击了可编辑的元素（输入框、按钮等）
    const target = e.target as HTMLElement;

    const isEditableElement =
      target.tagName === "INPUT" ||
      target.tagName === "BUTTON" ||
      target.closest("button") ||
      target.closest(".color-picker") ||
      target.closest(".format-toolbar");

    // 如果不是可编辑元素，则开始拖拽
    if (!isEditableElement) {
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
      dragStartOffset.current = { ...canvasOffset };
      e.preventDefault();
    }
  }, [canvasOffset]);

  return {
    isDragging,
    canvasOffset,
    handleMouseDown,
    cursor: isDragging ? "grabbing" : "grab",
  };
} 