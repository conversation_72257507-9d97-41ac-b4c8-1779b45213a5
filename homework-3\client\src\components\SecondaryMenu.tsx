import type { MenuType } from "../types/mindmap";
import { useMindMapStore } from "../store/mindMapStore";
import { StartMenu } from "./Secondary/StartMenu";
import { StyleMenu } from "./Secondary/StyleMenu";
import "./SecondaryMenu.css";

interface SecondaryMenuProps {
  menuType: MenuType;
}

export const SecondaryMenu = ({ menuType }: SecondaryMenuProps) => {
  // 使用全局状态管理
  const { selectedNode, mindMapData } = useMindMapStore();

  const currentNode = selectedNode ? mindMapData.nodes[selectedNode.id] : null;
  const isNodeSelected = !!selectedNode;

  const renderOtherMenus = () => (
    <div className="menu-container-wide">
      <span className="text-small text-gray">
        {menuType === "insert" && "插入功能"}
        {menuType === "view" && "视图功能"}
        {menuType === "export" && "导出功能"}
      </span>
    </div>
  );

  return (
    <div className="secondary-menu">
      {menuType === "start" && (
        <StartMenu isNodeSelected={isNodeSelected} currentNode={currentNode} />
      )}
      {menuType === "style" && (
        <StyleMenu isNodeSelected={isNodeSelected} currentNode={currentNode} />
      )}
      {(menuType === "insert" ||
        menuType === "view" ||
        menuType === "export") &&
        renderOtherMenus()}
    </div>
  );
};
