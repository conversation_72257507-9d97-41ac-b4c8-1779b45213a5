import { generateAiMindMapNodes } from "./aiClient";
import { useMindMapStore } from "../store/mindMapStore";

interface GenerateAiOptions {
  nodeId: string;
  aiPromptText: string;
  mindMapData: any;
  clearNodeChildren: (nodeId: string) => void;
  addAiGeneratedNode: (node: any) => void;
  setAiGenerating: (isGenerating: boolean) => void;
  setAiProgress: (progress: string) => void;
  onClose: () => void;
}

/**
 * AI创作思维导图节点的核心函数
 * 从ContextMenu组件中提取出来，便于复用和测试
 */
export const generateAiMindMap = async ({
  nodeId,
  aiPromptText,
  mindMapData,
  clearNodeChildren,
  addAiGeneratedNode,
  setAiGenerating,
  setAiProgress,
  onClose,
}: GenerateAiOptions) => {
  console.log("🤖 点击AI创作按钮，开始调用AI接口...");
  
  // 关闭右键菜单
  onClose();

  // 获取当前节点的文本内容
  const currentNode = mindMapData.nodes[nodeId];
  if (!currentNode) {
    console.error("❌ 没有找到当前节点");
    return;
  }

  // 添加调试信息
  console.log(" 调试信息:");
  console.log("  - aiPromptText:", `"${aiPromptText}"`);
  console.log("  - currentNode.text:", `"${currentNode.text}"`);
  console.log("  - nodeId:", nodeId);

  // 优先使用aiPromptText，如果为空则使用当前节点的文本
  const prompt = aiPromptText.trim();

  console.log("  prompt:", `"${prompt}"`);
  if (!prompt) {
    console.warn("⚠️ 请先在节点中输入内容作为AI生成的主题");
    alert("请先在节点中输入内容作为AI生成的主题");
    return;
  }

  // 确保有nodeId（当前右键点击的节点）
  if (!nodeId) {
    console.error("❌ 没有选中的节点");
    return;
  }

  // 检查当前节点是否有子节点，如果有则先清理
  if (currentNode && currentNode.children.length > 0) {
    console.log(
      `🧹 检测到节点有 ${currentNode.children.length} 个子节点，开始清理...`
    );
    clearNodeChildren(nodeId);
    console.log("✅ 子节点清理完成，开始AI创作");
  }

  console.log("📝 使用的prompt:", prompt);
  console.log("🎯 目标父节点ID:", nodeId);

  try {
    // 开始AI生成，显示加载状态
    setAiGenerating(true);
    setAiProgress("正在连接AI服务...");

    // 调用AI生成思维导图节点
    await generateAiMindMapNodes(prompt, nodeId, {
      onStart: () => {
        console.log("🚀 开始AI思维导图生成...");
        setAiProgress("AI正在分析主题内容...");
      },

      onNodeGenerated: (node) => {
        console.log("📦 生成新节点:", node);
        setAiProgress(`正在生成节点: ${node.text}`);
        // 立即添加到思维导图中，使用增量布局
        addAiGeneratedNode(node);
      },

      onEnd: (allNodes) => {
        console.log(
          "✅ AI思维导图生成完成，共生成",
          allNodes.length,
          "个节点"
        );
        setAiProgress("正在优化布局...");
        
        // AI生成完成后，进行一次性的布局优化
        const { relayoutLevel2Nodes, relayoutLevel3Nodes, mindMapData } = useMindMapStore.getState();
        const targetNode = mindMapData.nodes[nodeId];
        
        if (targetNode) {
          if (targetNode.level === 1) {
            // 如果是在level1节点下生成，重新布局所有level2节点
            setTimeout(() => {
              relayoutLevel2Nodes(nodeId);
            }, 100);
          } else if (targetNode.level === 2) {
            // 如果是在level2节点下生成，重新布局level3节点，然后布局level2
            setTimeout(() => {
              relayoutLevel3Nodes(nodeId);
              if (targetNode.parentId) {
                setTimeout(() => {
                  relayoutLevel2Nodes(targetNode.parentId!);
                }, 50);
              }
            }, 100);
          }
        }
        
        // 隐藏加载状态
        setAiGenerating(false);
        setAiProgress("");
      },

      onError: (error) => {
        console.error("❌ AI生成错误:", error);
        setAiGenerating(false);
        setAiProgress("");
        alert(`AI生成失败: ${error}`);
      },
    });
  } catch (error) {
    console.error("❌ AI创作失败:", error);
    setAiGenerating(false);
    setAiProgress("");
    alert(
      `AI创作失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};



