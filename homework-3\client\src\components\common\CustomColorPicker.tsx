import React, { useState, useEffect, useRef } from "react";
import { ChevronDown } from "lucide-react";
import "./CustomColorPicker.css";
import { systemColors } from "../../config/colors";

interface CustomColorPickerProps {
  color: string;
  onChange: (color: string) => void;
  onComplete: () => void;
}

const CustomColorPicker: React.FC<CustomColorPickerProps> = ({
  color,
  onChange,
  onComplete,
}) => {
  const [hexValue, setHexValue] = useState(color);
  const [recentColors, setRecentColors] = useState<string[]>([]);
  const [isRgbMode, setIsRgbMode] = useState(false);
  const hexInputRef = useRef<HTMLInputElement>(null);

  // 从 localStorage 加载最近使用的颜色
  useEffect(() => {
    const savedRecentColors = localStorage.getItem("recentColors");
    if (savedRecentColors) {
      try {
        const parsedColors = JSON.parse(savedRecentColors);
        setRecentColors(parsedColors);
      } catch (error) {
        console.error("Failed to parse recent colors:", error);
        // 如果解析失败，使用默认值
        setRecentColors(["#FADCDB"]);
      }
    } else {
      // 如果没有保存的数据，使用默认值
      setRecentColors(["#FADCDB"]);
    }
  }, []);

  // 保存最近使用的颜色到 localStorage
  const saveRecentColors = (colors: string[]) => {
    localStorage.setItem("recentColors", JSON.stringify(colors));
  };

  // Hex转RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  };

  // RGB转Hex
  const rgbToHex = (r: number, g: number, b: number) => {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  };

  // 获取RGB值
  const getRgbValues = () => {
    const rgb = hexToRgb(hexValue);
    return rgb ? [rgb.r, rgb.g, rgb.b] : [0, 0, 0];
  };

  const handleColorClick = (selectedColor: string) => {
    onChange(selectedColor);
    setHexValue(selectedColor);

    // 更新最近使用颜色
    const newRecentColors = [
      selectedColor,
      ...recentColors.filter((c) => c !== selectedColor),
    ].slice(0, 10); // 保持最多10个颜色

    setRecentColors(newRecentColors);
    saveRecentColors(newRecentColors);

    onComplete();
  };

  const handleHexChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setHexValue(value);

    // 验证十六进制格式
    if (/^#[0-9A-Fa-f]{6}$/.test(value)) {
      onChange(value);
    }
  };

  const handleRgbChange = (index: number, value: string) => {
    const numValue = parseInt(value) || 0;
    const clampedValue = Math.max(0, Math.min(255, numValue));
    const rgbValues = getRgbValues();
    rgbValues[index] = clampedValue;

    const newHex = rgbToHex(rgbValues[0], rgbValues[1], rgbValues[2]);
    setHexValue(newHex);
    onChange(newHex);
  };

  const handleHexKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      if (/^#[0-9A-Fa-f]{6}$/.test(hexValue)) {
        handleColorClick(hexValue);
      }
    }
  };

  const handleRgbKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleColorClick(hexValue);
    }
  };

  const toggleMode = () => {
    setIsRgbMode(!isRgbMode);
  };

  // 阻止点击事件冒泡到外部
  const handleContainerClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // 确保输入框初始状态为失焦
  useEffect(() => {
    if (hexInputRef.current) {
      hexInputRef.current.blur();
    }
  }, []);

  // 防止输入框自动选中文本
  useEffect(() => {
    if (hexInputRef.current) {
      // 延迟执行，确保组件完全渲染
      setTimeout(() => {
        if (hexInputRef.current) {
          hexInputRef.current.setSelectionRange(
            hexInputRef.current.value.length,
            hexInputRef.current.value.length
          );
        }
      }, 0);
    }
  }, []);

  return (
    <div className="custom-color-picker" onClick={handleContainerClick}>
      {/* 系统颜色 */}
      <div className="color-section">
        <div className="section-header">
          <span>系统色</span>
          <ChevronDown className="dropdown-arrow" />
        </div>
        <div className="system-colors-grid">
          {systemColors.map((row, rowIndex) => (
            <div key={rowIndex} className="color-row">
              {row.map((colorCode, colIndex) => (
                <div
                  key={`${rowIndex}-${colIndex}`}
                  className="color-swatch"
                  style={{ backgroundColor: colorCode }}
                  onClick={() => handleColorClick(colorCode)}
                  title={colorCode}
                />
              ))}
            </div>
          ))}
        </div>
      </div>

      {/* 最近使用 */}
      <div className="color-section">
        <div className="section-header">
          <span>最近使用</span>
        </div>
        <div className="recent-colors">
          {Array.from({ length: 10 }, (_, index) => {
            const colorCode = recentColors[index] || "";
            return (
              <div
                key={index}
                className={`color-swatch ${!colorCode ? "empty" : ""}`}
                style={{ backgroundColor: colorCode || "transparent" }}
                onClick={() => colorCode && handleColorClick(colorCode)}
                title={colorCode || "未使用"}
              />
            );
          })}
        </div>
      </div>

      {/* 十六进制/RGB输入 */}
      <div className="hex-input-section">
        <div className="hex-input-container">
          <svg
            className="eyedropper-icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="5245"
            width="16"
            height="16"
          >
            <path
              d="M881.777778 142.222222c-51.2-51.2-133.688889-51.2-184.888889 0l-108.088889 108.088889-42.666667-42.666667c-39.822222-39.822222-102.4-39.822222-142.222222 0s-39.822222 102.4 0 142.222223l42.666667 42.666666L73.955556 768c-5.688889 5.688889-8.533333 14.222222-8.533334 22.755556v153.6c0 8.533333 8.533333 17.066667 17.066667 17.066666h153.6c8.533333 0 17.066667-2.844444 22.755555-8.533333l372.622223-372.622222 42.666666 42.666666c39.822222 39.822222 102.4 39.822222 142.222223 0s39.822222-102.4 0-142.222222l-42.666667-42.666667 108.088889-108.088888c51.2-54.044444 51.2-136.533333 0-187.733334zM213.333333 878.933333H145.066667v-71.111111l349.866666-349.866666 71.111111 71.111111L213.333333 878.933333z"
              fillOpacity="0.65"
              p-id="5246"
            ></path>
          </svg>
          <div className="mode-toggle-container">
            <div
              className="mode-toggle"
              onClick={(e) => {
                e.stopPropagation(); // 额外保险
                toggleMode();
              }}
            >
              <span>{isRgbMode ? "RGB" : "Hex"}</span>
              <span className="toggle-arrow">↔</span>
            </div>

            {isRgbMode ? (
              <div className="rgb-inputs">
                {getRgbValues().map((value, index) => (
                  <input
                    key={index}
                    type="number"
                    value={value}
                    onChange={(e) => handleRgbChange(index, e.target.value)}
                    onKeyDown={handleRgbKeyDown}
                    className="rgb-input"
                    min="0"
                    max="255"
                  />
                ))}
              </div>
            ) : (
              <input
                ref={hexInputRef}
                type="text"
                value={hexValue}
                onChange={handleHexChange}
                onKeyDown={handleHexKeyDown}
                className="hex-input"
                placeholder="#000000"
                autoFocus={false}
                tabIndex={-1}
              />
            )}
          </div>

          <div
            className="color-preview"
            style={{ backgroundColor: hexValue }}
          />
        </div>
      </div>
    </div>
  );
};

export default CustomColorPicker;
