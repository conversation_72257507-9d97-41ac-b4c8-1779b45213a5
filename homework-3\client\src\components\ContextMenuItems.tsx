import React from "react";
import { ChevronRight } from "lucide-react";
import { add_icons } from "../config/add_menu";
import Ai_icon from "../icon/add/ai.png";
import addSon from "../icon/add/addSon.png";
import addBrother_icon from "../icon/start/brothers.png";
import addParents_icon from "../icon/start/parent.png";

interface ContextMenuItemsProps {
  nodeId: string;
  nodeLevel: number;
  onAddChild: (nodeId: string) => void;
  onAddSibling: (nodeId: string) => void;
  onDeleteNode: (nodeId: string) => void;
  onClose: () => void;
  generateAi: () => void;
}

interface MenuItemProps {
  icon: React.ReactNode;
  label: string;
  describe?: string;
  onClick: () => void;
  disabled?: boolean;
}

const MenuItem = ({
  icon,
  label,
  describe,
  onClick,
  disabled = false,
}: MenuItemProps) => (
  <button
    type="button"
    className="menu-item"
    onClick={disabled ? undefined : onClick}
    disabled={disabled}
  >
    <span className="menu-item-icon">{icon}</span>
    <span className="menu-item-label">{label}</span>
    {describe !== undefined && (
      <div className="menu-item-describe">
        {describe === "" ? <ChevronRight className="icon-small" /> : describe}
      </div>
    )}
  </button>
);

const MenuSeparator = () => <div className="menu-separator" />;

// 菜单项配置类型
interface MenuItemConfig {
  icon: string;
  label: string;
  describe: string;
  onClick: () => void;
  disabled: boolean;
}

export const ContextMenuItems = ({
  nodeId,
  nodeLevel,
  onAddChild,
  onAddSibling,
  onDeleteNode,
  onClose,
  generateAi,
}: ContextMenuItemsProps) => {
  // 处理菜单项点击
  const handleAddChild = () => {
    onAddChild(nodeId);
    onClose();
  };

  const handleAddSibling = () => {
    onAddSibling(nodeId);
    onClose();
  };

  const handleDelete = () => {
    onDeleteNode(nodeId);
    onClose();
  };

  // 根据节点级别获取基础菜单项配置
  const getBaseMenuItems = (): MenuItemConfig[] => {
    const baseItems: MenuItemConfig[] = [
      {
        icon: addSon,
        label: "新增子主题",
        describe: "Tab",
        onClick: handleAddChild,
        disabled: nodeLevel === 3, // 三级节点禁用
      },
      {
        icon: addBrother_icon,
        label: "新增同级主题",
        describe: "Enter",
        onClick: handleAddSibling,
        disabled: nodeLevel === 1, // 只有根节点禁用
      },
      {
        icon: addParents_icon,
        label: "新增父主题",
        describe: "Shift + Tab",
        onClick: () => onClose(),
        disabled: true, // 所有级别都禁用
      },
    ];

    // 如果是根节点，在开头添加AI创作
    if (nodeLevel === 1) {
      baseItems.unshift({
        icon: Ai_icon,
        label: "AI创作",
        describe: "",
        onClick: generateAi,
        disabled: false,
      });
    }

    return baseItems;
  };

  // 渲染通用菜单项的函数
  const renderCommonMenuItems = () => {
    return add_icons.map(({ icon, label, describe, onClick }, index) => (
      <React.Fragment key={`${label}-${index}`}>
        {/* 如果前一个菜单项有 addMenuSeparator 标记，则添加分隔符 */}
        {index > 0 && add_icons[index - 1].addMenuSeparator && (
          <MenuSeparator />
        )}
        <MenuItem
          icon={
            icon ? <img src={icon} alt={label} className="icon-small" /> : null
          }
          label={label}
          describe={describe}
          onClick={() => {
            // 如果nodeLevel不是1，并且label是'删除'，则使用handleDelete
            if (nodeLevel !== 1 && label === "删除") {
              handleDelete();
            } else {
              onClick();
            }
            onClose();
          }}
          disabled={(() => {
            // 根据节点级别和标签设置禁用状态
            if (nodeLevel === 1) {
              const level1DisabledLabels = [
                "收起主题",
                "选择主题",
                "剪切",
                "删除",
                "删除当前主题",
                "聚焦模式",
              ];
              return level1DisabledLabels.includes(label);
            }
            if (nodeLevel === 3) {
              const level3DisabledLabels = ["收起主题", "删除当前主题"];
              return level3DisabledLabels.includes(label);
            }
            return false; // 其他情况不禁用
          })()}
        />
      </React.Fragment>
    ));
  };

  // 渲染基础菜单项
  const renderBaseMenuItems = () => {
    return getBaseMenuItems().map((item, index) => (
      <MenuItem
        key={`${item.label}-${index}`}
        icon={<img src={item.icon} alt={item.label} className="icon-small" />}
        label={item.label}
        describe={item.describe}
        onClick={item.onClick}
        disabled={item.disabled}
      />
    ));
  };

  // 根据节点层级渲染菜单项
  if (nodeLevel < 1 || nodeLevel > 3) return null;

  return (
    <>
      {renderBaseMenuItems()}
      <MenuSeparator />
      {renderCommonMenuItems()}
    </>
  );
};
