import node_style_icon from "../icon/style/nodeStyle.png";
import shape_icon from "../icon/style/shape.png";
import node_background_icon from "../icon/style/nodeBackground.png";
import line_type_icon from "../icon/style/lineType.png";
import line_color_icon from "../icon/style/lineColor.png";
import line_width_icon from "../icon/style/lineWidth.png";
import border_width_icon from "../icon/style/borderWidth.png";
import border_color_icon from "../icon/style/borderColor.png";
import border_type_icon from "../icon/style/borderType.png";
import clean_icon from "../icon/style/clean.png";
import theme_height_icon from "../icon/style/themeHeight.png";
import theme_width_icon from "../icon/style/themeWidth.png";
import canvas_icon from "../icon/canvas.png";
import themes_icon from "../icon/themes.png";
import structure_icon from "../icon/structure.png";
import help_icon from "../icon/help.png";


import searchIcon from "../icon/start/search.png";
import quashIcon from "../icon/start/quash.png";
import recoverIcon from "../icon/start/recover.png";
import cleanIcon from "../icon/start/clean.png";
import leftIcon from "../icon/start/left.png";
import centerIcon from "../icon/start/center.png";
import rightIcon from "../icon/start/right.png";
import sonIcon from "../icon/start/son.png";
import brothersIcon from "../icon/start/brothers.png";
import parentIcon from "../icon/start/parent.png";
import outlineIcon from "../icon/start/outline.png";
import outFrameIcon from "../icon/start/outFrame.png";
import imageIcon from "../icon/start/image.png";
import linkIcon from "../icon/start/link.png";
import watermarkIcon from "../icon/start/watermark.png";
import hideIcon from "../icon/start/hide.png";
import showIcon from "../icon/start/show.png";


// 定义图标按钮配置的类型
export interface IconConfig {
  icon: string;   // 图标的路径
  label: string;  // 按钮的标签
  onClick: () => void;  // 按钮点击事件
  addSeparator?: boolean; // 是否在按钮后添加 Separator
  showDropdownArrow?: boolean; // 是否显示下拉箭头，默认为 true
  iconOnly?: boolean; // 是否只显示图标，不显示文本和下拉箭头
}

// 按钮配置数组，包含图标、标签和点击事件
export const style_icons: IconConfig[] = [
  { icon: node_style_icon, label: '节点样式', onClick: () => {} },
  { icon: shape_icon, label: '形状', onClick: () => {} },
  { icon: node_background_icon, label: '节点背景', onClick: () => {}, addSeparator: true },  // 在后面添加 Separator
  { icon: line_type_icon, label: '连线类型', onClick: () => {} },
  { icon: line_color_icon, label: '连线颜色', onClick: () => {} },
  { icon: line_width_icon, label: '连线宽度', onClick: () => {}, addSeparator: true  },
  { icon: border_width_icon, label: '边框宽度', onClick: () => {}}, // 在后面添加 Separator
  { icon: border_color_icon, label: '边框颜色', onClick: () => {} },
  { icon: border_type_icon, label: '边框类型', onClick: () => {}, addSeparator: true }, // 在后面添加 Separator
  { icon: clean_icon, label: '清除样式', onClick: () => {}, addSeparator: true }, // 在后面添加 Separator
  { icon: canvas_icon, label: '画布', onClick: () => {} },
  { icon: themes_icon, label: '风格', onClick: () => {} },
  { icon: structure_icon, label: '结构', onClick: () => {}, addSeparator: true }, // 在后面添加 Separator
  { icon: theme_height_icon, label: '主题间距', onClick: () => {} },
  { icon: theme_width_icon, label: '主题宽度', onClick: () => {} },
  { icon: help_icon, label: '帮助', onClick: () => {},iconOnly: true  },
];


// 按钮配置数组，包含图标、标签和点击事件
export const start_t_icons: IconConfig[] = [
  { icon: searchIcon, label: '查找替换', onClick: () => {}, addSeparator: true ,showDropdownArrow: false},
  { icon: quashIcon, label: '撤销（Ctrl+Z）', onClick: () => {}, iconOnly: true },
  { icon: recoverIcon, label: '恢复（Ctrl+Y）', onClick: () => {}, iconOnly: true },
  { icon: cleanIcon, label: '格式刷（Ctrl+Shift+C）', onClick: () => {}, addSeparator: true, iconOnly: true },
  { icon: '', label: '微软雅黑', onClick: () => {}, iconOnly: false , }, // 显示文本和下拉箭头，无图标
  { icon: '', label: '14px', onClick: () => {}, iconOnly: false,}, // 显示文本和下拉箭头，无图标
];

export const start_b_icons: IconConfig[] = [
  { icon: leftIcon, label: '居左对齐', onClick: () => {}, iconOnly: true },
  { icon: centerIcon, label: '居中对齐', onClick: () => {}, iconOnly: true },
  { icon: rightIcon, label: '居右对齐', onClick: () => {}, addSeparator: true, iconOnly: true },
  { icon: sonIcon, label: '子主题 (tab, insert)', onClick: () => {}, iconOnly: true },
  { icon: brothersIcon, label: '同级主题 (enter)', onClick: () => {}, iconOnly: true },
  { icon: parentIcon, label: '父主题 (shift+tab)', onClick: () => {}, addSeparator: true, iconOnly: true },
  { icon: outlineIcon, label: '概要', onClick: () => {},showDropdownArrow: false  },
  { icon: outFrameIcon, label: '外框', onClick: () => {}, showDropdownArrow: false },
  { icon: imageIcon, label: '图片', onClick: () => {}, showDropdownArrow: false },
  { icon: linkIcon, label: '超链接', onClick: () => {}, showDropdownArrow: false  },
  { icon: watermarkIcon, label: '水印', onClick: () => {}, addSeparator: true },
  { icon: canvas_icon, label: '画布', onClick: () => {} },
  { icon: themes_icon, label: '风格', onClick: () => {} },
  { icon: structure_icon, label: '结构', onClick: () => {} },
  { icon: hideIcon, label: '收起', onClick: () => {} },
  { icon: showIcon, label: '展开', onClick: () => {} },
  { icon: help_icon, label: '帮助', onClick: () => {}, iconOnly: true },
];