import * as React from "react";
import * as SeparatorPrimitive from "@radix-ui/react-separator";
import "./separator.css";

const Separator = React.forwardRef<
  React.ElementRef<typeof SeparatorPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>
>(
  (
    { className = "", orientation = "horizontal", decorative = true, ...props },
    ref
  ) => {
    const getSeparatorClasses = () => {
      let classes = "separator";

      if (orientation === "horizontal") {
        classes += " separator-horizontal";
      } else {
        classes += " separator-vertical";
      }

      if (className) {
        classes += ` ${className}`;
      }

      return classes;
    };

    return (
      <SeparatorPrimitive.Root
        ref={ref}
        decorative={decorative}
        orientation={orientation}
        className={getSeparatorClasses()}
        {...props}
      />
    );
  }
);
Separator.displayName = SeparatorPrimitive.Root.displayName;

export { Separator };
