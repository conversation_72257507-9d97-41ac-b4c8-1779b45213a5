.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  white-space: nowrap;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;
  outline: none;
}

.btn:focus-visible {
  outline: 1px solid #3b82f6;
  outline-offset: 2px;
}

.btn:disabled {
  pointer-events: none;
  opacity: 0.5;
}

.btn svg {
  pointer-events: none;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.margin-left-medium .dropdown-arrow{
  pointer-events: none;
    width: 10px;
    height: 10px;
    flex-shrink: 0;
}

/* 按钮变体 */
.btn-default {
  background-color: #0f172a;
  /* color: white; */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn-default:hover {
  background-color: rgba(15, 23, 42, 0.9);
}

.btn-destructive {
  background-color: #dc2626;
  color: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn-destructive:hover {
  background-color: rgba(220, 38, 38, 0.9);
}

.btn-outline {
  border: 1px solid #e5e7eb;
  background-color: white;
  box-shadow: 0 1px 2px 0 #cbd5e1;
  color: #374151;
}

.btn-outline:hover {
  background-color: #f9fafb;
  color: #111827;
}

.btn-secondary {
  background-color: #f1f5f9;
  color: #0f172a;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  background-color: rgba(241, 245, 249, 0.8);
}

.btn-ghost {
  background-color: transparent;
  color: #374151;
}

.btn-ghost:hover {
  background-color: #f9fafb;
  color: #111827;
}

.btn-link {
  color: #0f172a;
  text-decoration: underline;
  text-underline-offset: 4px;
  background-color: transparent;
}

.btn-link:hover {
  text-decoration: underline;
}

/* 按钮尺寸 */
.btn-default-size {
  height: 36px;
  padding: 8px 16px;
}

.btn-sm {
  height: 32px;
  border-radius: 7px;
  padding: 0 12px;
  font-size: 12px;
}

.btn-lg {
  height: 40px;
  border-radius: 6px;
  padding: 0 32px;
}

.btn-icon {
  height: 36px;
  width: 36px;
  padding: 0;
}
