/* Tooltip 组件样式 */

.tooltip-content {
  z-index: 50;
  overflow: hidden;
  border-radius: 6px;
  background-color: #0f172a;
  padding: 6px 12px;
  font-size: 12px;
  color: white;
  animation: tooltip-in 0.15s ease-out;
  transform-origin: var(--radix-tooltip-content-transform-origin);
}

.tooltip-content[data-state="closed"] {
  animation: tooltip-out 0.15s ease-in;
}

/* 动画定义 */
@keyframes tooltip-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes tooltip-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* 滑入动画 */
.tooltip-content[data-side="bottom"] {
  animation: tooltip-in 0.15s ease-out, slide-in-from-top 0.15s ease-out;
}

.tooltip-content[data-side="top"] {
  animation: tooltip-in 0.15s ease-out, slide-in-from-bottom 0.15s ease-out;
}

.tooltip-content[data-side="left"] {
  animation: tooltip-in 0.15s ease-out, slide-in-from-right 0.15s ease-out;
}

.tooltip-content[data-side="right"] {
  animation: tooltip-in 0.15s ease-out, slide-in-from-left 0.15s ease-out;
}

@keyframes slide-in-from-top {
  from {
    transform: translateY(-8px);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom {
  from {
    transform: translateY(8px);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-from-left {
  from {
    transform: translateX(-8px);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slide-in-from-right {
  from {
    transform: translateX(8px);
  }
  to {
    transform: translateX(0);
  }
}
