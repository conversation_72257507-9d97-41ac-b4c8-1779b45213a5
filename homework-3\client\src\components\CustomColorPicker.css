/* 自定义颜色选择器样式 */
.custom-color-picker {
  padding: 16px;
  background: white;
  border-radius: 8px;
  min-width: 320px;
}

.color-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: start;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  padding: 6px;
  cursor: pointer;
  width: fit-content;
}

.section-header:hover {
  border-radius: 6px;
  background-color: #f5f5f5;
}

.section-header .dropdown-arrow {
  width: 16px;
  height: 16px;
  color: #666;
  margin-left: 8px;
}

.section-header span {
  color: #333;
  font-weight: 500;
}

.system-colors-grid {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.color-row {
  display: flex;
  gap: 4px;
}

.color-swatch {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.color-swatch:hover {
  transform: scale(1.1);
  border-color: #999;
}

.color-swatch.empty {
  background: transparent;
  border: 1px dashed #ccc;
}

.recent-colors {
  display: flex;
  gap: 4px;
}

.hex-input-section {
  border-top: 1px solid #e0e0e0;
  padding-top: 12px;
}

.hex-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.eyedropper-icon {
  color: #666;
  flex-shrink: 0; /* 防止图标被压缩 */
}

/* mode-toggle-container 占据剩余空间 */
.mode-toggle-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1; /* 占据剩余所有空间 */
  min-height: 32px;
}

.mode-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.mode-toggle:hover {
  background-color: #f0f0f0;
}

.toggle-arrow {
  font-size: 12px;
  color: #666;
}

.hex-input {
  flex: 1; /* 在mode-toggle-container内占据剩余空间 */
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-family: monospace;
  min-width: 0; /* 允许flex项目收缩 */
}

.hex-input:focus {
  outline: none;
  border-color: #007bff;
}

.rgb-inputs {
  display: flex;
  gap: 2px;
  flex: 1; /* 在mode-toggle-container内占据剩余空间 */
}

.rgb-input {
  flex: 1;
  padding: 4px 2px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  font-family: monospace;
  min-width: 0;
}

.rgb-input:focus {
  outline: none;
  border-color: #007bff;
}

.color-preview {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  border: 1px solid #ddd;
  flex-shrink: 0; /* 防止预览框被压缩 */
} 