.ai-loading-indicator {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 280px;
  animation: slideInFromTop 0.3s ease-out;
}



.ai-loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ai-loading-spinner {
  position: relative;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #3b82f6;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #10b981;
  animation-delay: 0.3s;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #f59e0b;
  animation-delay: 0.6s;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.ai-loading-text {
  flex: 1;
  min-width: 0;
}

.ai-loading-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.2;
}

.ai-loading-progress {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.3;
}
