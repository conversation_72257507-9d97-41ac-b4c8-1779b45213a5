/* 主画布容器 */
.mindmap-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: auto;
  background-color: #F9FAFB;
  /* 确保滚动条在需要时出现 */
  overflow-x: auto;
  overflow-y: auto;
  /* 平滑滚动 */
  scroll-behavior: smooth;
}

/* 画布内容容器 - 定义实际的滚动区域尺寸 */
.canvas-content {
  position: relative;
}

/* SVG 连接线层 */
.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  overflow: visible;
}

/* 节点层容器 */
.nodes-layer {
  position: relative;
}

/* 连接线样式 */
.connection-line {
  pointer-events: none;
}
