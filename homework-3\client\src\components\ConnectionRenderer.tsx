import { useCallback } from "react";
import type { MindMapData, MindMapNode as NodeType } from "../types/mindmap";

interface ConnectionRendererProps {
  mindMapData: MindMapData;
  nodeDimensions: Record<string, { width: number; height: number }>;
  svgBounds: {
    width: number;
    height: number;
    minX: number;
    minY: number;
  };
  canvasOffset: { x: number; y: number };
  getNodeDimensions: (node: NodeType) => { width: number; height: number };
}

export default function ConnectionRenderer({
  mindMapData,
  nodeDimensions,
  svgBounds,
  canvasOffset,
  getNodeDimensions,
}: ConnectionRendererProps) {
  // 计算连接线路径
  const getConnectionPath = useCallback(
    (parent: NodeType, child: NodeType): string => {
      const parentDim = getNodeDimensions(parent);
      const childDim = getNodeDimensions(child);

      // 计算父节点右边框中心点作为起点
      const startX = parent.position.x + parentDim.width;
      const startY = parent.position.y + parentDim.height / 2;

      // 计算子节点左边框中心点作为终点（紧贴节点边框）
      const endX = child.position.x;
      const endY = child.position.y + childDim.height / 2;

      // 根据父节点层级和子节点个数决定连接方式
      const childrenCount = parent.children.length;
      const childIndex = parent.children.indexOf(child.id);

      let useStraightLine = false;

      if (parent.level === 1) {
        // level1节点：永远使用贝塞尔曲线连接
        useStraightLine = false;
      } else if (parent.level === 2) {
        // level2节点：应用特殊的连接规则
        if (childrenCount % 2 === 1) {
          // 奇数个子节点：中间那个子节点使用直线连接
          const middleIndex = Math.floor(childrenCount / 2);
          useStraightLine = childIndex === middleIndex;
        } else {
          // 偶数个子节点：所有子节点都使用贝塞尔曲线连接
          useStraightLine = false;
        }
      } else {
        // level3及以上节点：所有连接都使用贝塞尔曲线
        useStraightLine = false;
      }

      if (useStraightLine) {
        // 使用直线连接
        return `M ${startX} ${startY} L ${endX} ${endY}`;
      } else {
        // 使用贝塞尔曲线连接
        const controlX1 = startX + (endX - startX) * 0.3;
        const controlY1 = startY;
        const controlX2 = startX + (endX - startX) * 0.7;
        const controlY2 = endY;

        return `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`;
      }
    },
    [getNodeDimensions]
  );

  // 渲染所有连接线
  const renderConnections = () => {
    const connections: JSX.Element[] = [];

    for (const node of Object.values(mindMapData.nodes)) {
      // 如果节点被收缩，不渲染到其子节点的连接线
      if (node.collapsed) {
        continue;
      }

      for (const childId of node.children) {
        const childNode = mindMapData.nodes[childId];
        if (childNode) {
          const path = getConnectionPath(node, childNode);
          connections.push(
            <path
              key={`${node.id}-${childId}`}
              d={path}
              stroke="#666"
              strokeWidth="2"
              fill="none"
              className="connection-line"
            />
          );
        }
      }
    }

    return connections;
  };

  return (
    <svg
      className="connections-layer"
      width="100%"
      height="100%"
      style={{
        transform: `translate(${-svgBounds.minX + canvasOffset.x}px, ${-svgBounds.minY + canvasOffset.y}px)`,
      }}
    >
      {renderConnections()}
    </svg>
  );
}
