import { useMemo } from "react";
import type { MindMapData } from "../types/mindmap";

interface SVGBounds {
  width: number;
  height: number;
  minX: number;
  minY: number;
}

interface UseSVGBoundsReturn {
  svgBounds: SVGBounds;
}

export function useSVGBounds(mindMapData: MindMapData): UseSVGBoundsReturn {
  const svgBounds = useMemo(() => {
    const nodes = Object.values(mindMapData.nodes);
    if (nodes.length === 0)
      return { width: 1000, height: 800, minX: 0, minY: 0 };

    let minX = Number.POSITIVE_INFINITY;
    let minY = Number.POSITIVE_INFINITY;
    let maxX = Number.NEGATIVE_INFINITY;
    let maxY = Number.NEGATIVE_INFINITY;

    for (const node of nodes) {
      minX = Math.min(minX, node.position.x);
      minY = Math.min(minY, node.position.y);
      maxX = Math.max(maxX, node.position.x + 200); // 节点宽度
      maxY = Math.max(maxY, node.position.y + 40); // 节点高度
    }

    return {
      width: Math.max(1000, maxX - minX + 500),
      height: Math.max(800, maxY - minY + 500),
      minX: Math.min(0, minX - 100),
      minY: Math.min(0, minY - 100),
    };
  }, [mindMapData.nodes]);

  return { svgBounds };
} 