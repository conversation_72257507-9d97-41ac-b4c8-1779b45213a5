import { useEffect, useRef } from "react";
import "./ContextMenu.css";
import { generateAiMindMap } from "../utils/mindMapAi";
import { useMindMapStore } from "../store/mindMapStore";
import { ContextMenuItems } from "./ContextMenuItems";

interface ContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number };
  nodeId?: string;
  nodeLevel?: number;
  onClose: () => void;
  onAddChild: (nodeId: string) => void;
  onAddSibling: (nodeId: string) => void;
  onDeleteNode: (nodeId: string) => void;
}

const ContextMenu = ({
  isOpen,
  position,
  nodeId,
  nodeLevel,
  onClose,
  onAddChild,
  onAddSibling,
  onDeleteNode,
}: ContextMenuProps) => {
  const menuRef = useRef<HTMLDivElement>(null);

  // 使用全局状态获取AI prompt文本和节点操作方法
  const {
    aiPromptText,
    addAiGeneratedNode,
    setAiGenerating,
    clearNodeChildren,
    mindMapData,
  } = useMindMapStore();

  // 定义generateAi函数，调用AI接口生成思维导图节点
  const generateAi = async () => {
    await generateAiMindMap({
      nodeId: nodeId!,
      aiPromptText,
      mindMapData,
      clearNodeChildren,
      addAiGeneratedNode,
      setAiGenerating,
      onClose,
    });
  };

  // 处理点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  // 计算菜单位置，防止超出屏幕
  const getMenuPosition = () => {
    const menuWidth = 180;
    const menuHeight = 200; // 估算高度
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    let x = position.x;
    let y = position.y;

    // 防止超出右边界
    if (x + menuWidth > screenWidth) {
      x = screenWidth - menuWidth - 10;
    }

    // 防止超出底边界
    if (y + menuHeight > screenHeight) {
      y = screenHeight - menuHeight - 10;
    }

    // 防止超出左边界
    if (x < 0) {
      x = 10;
    }

    // 防止超出顶边界
    if (y < 0) {
      y = 10;
    }

    return { x, y };
  };

  if (!isOpen || !nodeId || !nodeLevel) return null;

  const menuPosition = getMenuPosition();

  return (
    <div
      ref={menuRef}
      className="context-menu"
      style={{
        left: menuPosition.x,
        bottom: "5px",
      }}
    >
      <ContextMenuItems
        nodeId={nodeId}
        nodeLevel={nodeLevel}
        onAddChild={onAddChild}
        onAddSibling={onAddSibling}
        onDeleteNode={onDeleteNode}
        onClose={onClose}
        generateAi={generateAi}
      />
    </div>
  );
};

export default ContextMenu;
