function zm(r,l){for(var s=0;s<l.length;s++){const u=l[s];if(typeof u!="string"&&!Array.isArray(u)){for(const c in u)if(c!=="default"&&!(c in r)){const f=Object.getOwnPropertyDescriptor(u,c);f&&Object.defineProperty(r,c,f.get?f:{enumerable:!0,get:()=>u[c]})}}}return Object.freeze(Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}))}(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))u(c);new MutationObserver(c=>{for(const f of c)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&u(d)}).observe(document,{childList:!0,subtree:!0});function s(c){const f={};return c.integrity&&(f.integrity=c.integrity),c.referrerPolicy&&(f.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?f.credentials="include":c.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function u(c){if(c.ep)return;c.ep=!0;const f=s(c);fetch(c.href,f)}})();function wd(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var Js={exports:{}},go={},Xs={exports:{}},Ae={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mf;function Um(){if(Mf)return Ae;Mf=1;var r=Symbol.for("react.element"),l=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),f=Symbol.for("react.provider"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),A=Symbol.iterator;function C(P){return P===null||typeof P!="object"?null:(P=A&&P[A]||P["@@iterator"],typeof P=="function"?P:null)}var S={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,E={};function I(P,Q,ue){this.props=P,this.context=Q,this.refs=E,this.updater=ue||S}I.prototype.isReactComponent={},I.prototype.setState=function(P,Q){if(typeof P!="object"&&typeof P!="function"&&P!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,P,Q,"setState")},I.prototype.forceUpdate=function(P){this.updater.enqueueForceUpdate(this,P,"forceUpdate")};function j(){}j.prototype=I.prototype;function B(P,Q,ue){this.props=P,this.context=Q,this.refs=E,this.updater=ue||S}var L=B.prototype=new j;L.constructor=B,R(L,I.prototype),L.isPureReactComponent=!0;var F=Array.isArray,M=Object.prototype.hasOwnProperty,z={current:null},U={key:!0,ref:!0,__self:!0,__source:!0};function J(P,Q,ue){var ce,he={},fe=null,ve=null;if(Q!=null)for(ce in Q.ref!==void 0&&(ve=Q.ref),Q.key!==void 0&&(fe=""+Q.key),Q)M.call(Q,ce)&&!U.hasOwnProperty(ce)&&(he[ce]=Q[ce]);var de=arguments.length-2;if(de===1)he.children=ue;else if(1<de){for(var we=Array(de),Ee=0;Ee<de;Ee++)we[Ee]=arguments[Ee+2];he.children=we}if(P&&P.defaultProps)for(ce in de=P.defaultProps,de)he[ce]===void 0&&(he[ce]=de[ce]);return{$$typeof:r,type:P,key:fe,ref:ve,props:he,_owner:z.current}}function X(P,Q){return{$$typeof:r,type:P.type,key:Q,ref:P.ref,props:P.props,_owner:P._owner}}function oe(P){return typeof P=="object"&&P!==null&&P.$$typeof===r}function me(P){var Q={"=":"=0",":":"=2"};return"$"+P.replace(/[=:]/g,function(ue){return Q[ue]})}var xe=/\/+/g;function ye(P,Q){return typeof P=="object"&&P!==null&&P.key!=null?me(""+P.key):Q.toString(36)}function Y(P,Q,ue,ce,he){var fe=typeof P;(fe==="undefined"||fe==="boolean")&&(P=null);var ve=!1;if(P===null)ve=!0;else switch(fe){case"string":case"number":ve=!0;break;case"object":switch(P.$$typeof){case r:case l:ve=!0}}if(ve)return ve=P,he=he(ve),P=ce===""?"."+ye(ve,0):ce,F(he)?(ue="",P!=null&&(ue=P.replace(xe,"$&/")+"/"),Y(he,Q,ue,"",function(Ee){return Ee})):he!=null&&(oe(he)&&(he=X(he,ue+(!he.key||ve&&ve.key===he.key?"":(""+he.key).replace(xe,"$&/")+"/")+P)),Q.push(he)),1;if(ve=0,ce=ce===""?".":ce+":",F(P))for(var de=0;de<P.length;de++){fe=P[de];var we=ce+ye(fe,de);ve+=Y(fe,Q,ue,we,he)}else if(we=C(P),typeof we=="function")for(P=we.call(P),de=0;!(fe=P.next()).done;)fe=fe.value,we=ce+ye(fe,de++),ve+=Y(fe,Q,ue,we,he);else if(fe==="object")throw Q=String(P),Error("Objects are not valid as a React child (found: "+(Q==="[object Object]"?"object with keys {"+Object.keys(P).join(", ")+"}":Q)+"). If you meant to render a collection of children, use an array instead.");return ve}function Z(P,Q,ue){if(P==null)return P;var ce=[],he=0;return Y(P,ce,"","",function(fe){return Q.call(ue,fe,he++)}),ce}function le(P){if(P._status===-1){var Q=P._result;Q=Q(),Q.then(function(ue){(P._status===0||P._status===-1)&&(P._status=1,P._result=ue)},function(ue){(P._status===0||P._status===-1)&&(P._status=2,P._result=ue)}),P._status===-1&&(P._status=0,P._result=Q)}if(P._status===1)return P._result.default;throw P._result}var te={current:null},_={transition:null},b={ReactCurrentDispatcher:te,ReactCurrentBatchConfig:_,ReactCurrentOwner:z};function G(){throw Error("act(...) is not supported in production builds of React.")}return Ae.Children={map:Z,forEach:function(P,Q,ue){Z(P,function(){Q.apply(this,arguments)},ue)},count:function(P){var Q=0;return Z(P,function(){Q++}),Q},toArray:function(P){return Z(P,function(Q){return Q})||[]},only:function(P){if(!oe(P))throw Error("React.Children.only expected to receive a single React element child.");return P}},Ae.Component=I,Ae.Fragment=s,Ae.Profiler=c,Ae.PureComponent=B,Ae.StrictMode=u,Ae.Suspense=m,Ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=b,Ae.act=G,Ae.cloneElement=function(P,Q,ue){if(P==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+P+".");var ce=R({},P.props),he=P.key,fe=P.ref,ve=P._owner;if(Q!=null){if(Q.ref!==void 0&&(fe=Q.ref,ve=z.current),Q.key!==void 0&&(he=""+Q.key),P.type&&P.type.defaultProps)var de=P.type.defaultProps;for(we in Q)M.call(Q,we)&&!U.hasOwnProperty(we)&&(ce[we]=Q[we]===void 0&&de!==void 0?de[we]:Q[we])}var we=arguments.length-2;if(we===1)ce.children=ue;else if(1<we){de=Array(we);for(var Ee=0;Ee<we;Ee++)de[Ee]=arguments[Ee+2];ce.children=de}return{$$typeof:r,type:P.type,key:he,ref:fe,props:ce,_owner:ve}},Ae.createContext=function(P){return P={$$typeof:d,_currentValue:P,_currentValue2:P,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},P.Provider={$$typeof:f,_context:P},P.Consumer=P},Ae.createElement=J,Ae.createFactory=function(P){var Q=J.bind(null,P);return Q.type=P,Q},Ae.createRef=function(){return{current:null}},Ae.forwardRef=function(P){return{$$typeof:h,render:P}},Ae.isValidElement=oe,Ae.lazy=function(P){return{$$typeof:y,_payload:{_status:-1,_result:P},_init:le}},Ae.memo=function(P,Q){return{$$typeof:g,type:P,compare:Q===void 0?null:Q}},Ae.startTransition=function(P){var Q=_.transition;_.transition={};try{P()}finally{_.transition=Q}},Ae.unstable_act=G,Ae.useCallback=function(P,Q){return te.current.useCallback(P,Q)},Ae.useContext=function(P){return te.current.useContext(P)},Ae.useDebugValue=function(){},Ae.useDeferredValue=function(P){return te.current.useDeferredValue(P)},Ae.useEffect=function(P,Q){return te.current.useEffect(P,Q)},Ae.useId=function(){return te.current.useId()},Ae.useImperativeHandle=function(P,Q,ue){return te.current.useImperativeHandle(P,Q,ue)},Ae.useInsertionEffect=function(P,Q){return te.current.useInsertionEffect(P,Q)},Ae.useLayoutEffect=function(P,Q){return te.current.useLayoutEffect(P,Q)},Ae.useMemo=function(P,Q){return te.current.useMemo(P,Q)},Ae.useReducer=function(P,Q,ue){return te.current.useReducer(P,Q,ue)},Ae.useRef=function(P){return te.current.useRef(P)},Ae.useState=function(P){return te.current.useState(P)},Ae.useSyncExternalStore=function(P,Q,ue){return te.current.useSyncExternalStore(P,Q,ue)},Ae.useTransition=function(){return te.current.useTransition()},Ae.version="18.3.1",Ae}var jf;function Sa(){return jf||(jf=1,Xs.exports=Um()),Xs.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lf;function Qm(){if(Lf)return go;Lf=1;var r=Sa(),l=Symbol.for("react.element"),s=Symbol.for("react.fragment"),u=Object.prototype.hasOwnProperty,c=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function d(h,m,g){var y,A={},C=null,S=null;g!==void 0&&(C=""+g),m.key!==void 0&&(C=""+m.key),m.ref!==void 0&&(S=m.ref);for(y in m)u.call(m,y)&&!f.hasOwnProperty(y)&&(A[y]=m[y]);if(h&&h.defaultProps)for(y in m=h.defaultProps,m)A[y]===void 0&&(A[y]=m[y]);return{$$typeof:l,type:h,key:C,ref:S,props:A,_owner:c.current}}return go.Fragment=s,go.jsx=d,go.jsxs=d,go}var Bf;function Vm(){return Bf||(Bf=1,Js.exports=Qm()),Js.exports}var v=Vm(),w=Sa();const Wn=wd(w),xd=zm({__proto__:null,default:Wn},[w]);var jl={},$s={exports:{}},lt={},Zs={exports:{}},qs={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ff;function Wm(){return Ff||(Ff=1,function(r){function l(_,b){var G=_.length;_.push(b);e:for(;0<G;){var P=G-1>>>1,Q=_[P];if(0<c(Q,b))_[P]=b,_[G]=Q,G=P;else break e}}function s(_){return _.length===0?null:_[0]}function u(_){if(_.length===0)return null;var b=_[0],G=_.pop();if(G!==b){_[0]=G;e:for(var P=0,Q=_.length,ue=Q>>>1;P<ue;){var ce=2*(P+1)-1,he=_[ce],fe=ce+1,ve=_[fe];if(0>c(he,G))fe<Q&&0>c(ve,he)?(_[P]=ve,_[fe]=G,P=fe):(_[P]=he,_[ce]=G,P=ce);else if(fe<Q&&0>c(ve,G))_[P]=ve,_[fe]=G,P=fe;else break e}}return b}function c(_,b){var G=_.sortIndex-b.sortIndex;return G!==0?G:_.id-b.id}if(typeof performance=="object"&&typeof performance.now=="function"){var f=performance;r.unstable_now=function(){return f.now()}}else{var d=Date,h=d.now();r.unstable_now=function(){return d.now()-h}}var m=[],g=[],y=1,A=null,C=3,S=!1,R=!1,E=!1,I=typeof setTimeout=="function"?setTimeout:null,j=typeof clearTimeout=="function"?clearTimeout:null,B=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function L(_){for(var b=s(g);b!==null;){if(b.callback===null)u(g);else if(b.startTime<=_)u(g),b.sortIndex=b.expirationTime,l(m,b);else break;b=s(g)}}function F(_){if(E=!1,L(_),!R)if(s(m)!==null)R=!0,le(M);else{var b=s(g);b!==null&&te(F,b.startTime-_)}}function M(_,b){R=!1,E&&(E=!1,j(J),J=-1),S=!0;var G=C;try{for(L(b),A=s(m);A!==null&&(!(A.expirationTime>b)||_&&!me());){var P=A.callback;if(typeof P=="function"){A.callback=null,C=A.priorityLevel;var Q=P(A.expirationTime<=b);b=r.unstable_now(),typeof Q=="function"?A.callback=Q:A===s(m)&&u(m),L(b)}else u(m);A=s(m)}if(A!==null)var ue=!0;else{var ce=s(g);ce!==null&&te(F,ce.startTime-b),ue=!1}return ue}finally{A=null,C=G,S=!1}}var z=!1,U=null,J=-1,X=5,oe=-1;function me(){return!(r.unstable_now()-oe<X)}function xe(){if(U!==null){var _=r.unstable_now();oe=_;var b=!0;try{b=U(!0,_)}finally{b?ye():(z=!1,U=null)}}else z=!1}var ye;if(typeof B=="function")ye=function(){B(xe)};else if(typeof MessageChannel<"u"){var Y=new MessageChannel,Z=Y.port2;Y.port1.onmessage=xe,ye=function(){Z.postMessage(null)}}else ye=function(){I(xe,0)};function le(_){U=_,z||(z=!0,ye())}function te(_,b){J=I(function(){_(r.unstable_now())},b)}r.unstable_IdlePriority=5,r.unstable_ImmediatePriority=1,r.unstable_LowPriority=4,r.unstable_NormalPriority=3,r.unstable_Profiling=null,r.unstable_UserBlockingPriority=2,r.unstable_cancelCallback=function(_){_.callback=null},r.unstable_continueExecution=function(){R||S||(R=!0,le(M))},r.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):X=0<_?Math.floor(1e3/_):5},r.unstable_getCurrentPriorityLevel=function(){return C},r.unstable_getFirstCallbackNode=function(){return s(m)},r.unstable_next=function(_){switch(C){case 1:case 2:case 3:var b=3;break;default:b=C}var G=C;C=b;try{return _()}finally{C=G}},r.unstable_pauseExecution=function(){},r.unstable_requestPaint=function(){},r.unstable_runWithPriority=function(_,b){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var G=C;C=_;try{return b()}finally{C=G}},r.unstable_scheduleCallback=function(_,b,G){var P=r.unstable_now();switch(typeof G=="object"&&G!==null?(G=G.delay,G=typeof G=="number"&&0<G?P+G:P):G=P,_){case 1:var Q=-1;break;case 2:Q=250;break;case 5:Q=**********;break;case 4:Q=1e4;break;default:Q=5e3}return Q=G+Q,_={id:y++,callback:b,priorityLevel:_,startTime:G,expirationTime:Q,sortIndex:-1},G>P?(_.sortIndex=G,l(g,_),s(m)===null&&_===s(g)&&(E?(j(J),J=-1):E=!0,te(F,G-P))):(_.sortIndex=Q,l(m,_),R||S||(R=!0,le(M))),_},r.unstable_shouldYield=me,r.unstable_wrapCallback=function(_){var b=C;return function(){var G=C;C=b;try{return _.apply(this,arguments)}finally{C=G}}}}(qs)),qs}var _f;function Hm(){return _f||(_f=1,Zs.exports=Wm()),Zs.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zf;function Ym(){if(zf)return lt;zf=1;var r=Sa(),l=Hm();function s(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var u=new Set,c={};function f(e,t){d(e,t),d(e+"Capture",t)}function d(e,t){for(c[e]=t,e=0;e<t.length;e++)u.add(t[e])}var h=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),m=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,y={},A={};function C(e){return m.call(A,e)?!0:m.call(y,e)?!1:g.test(e)?A[e]=!0:(y[e]=!0,!1)}function S(e,t,n,o){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return o?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function R(e,t,n,o){if(t===null||typeof t>"u"||S(e,t,n,o))return!0;if(o)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function E(e,t,n,o,i,a,p){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=o,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=p}var I={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){I[e]=new E(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];I[t]=new E(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){I[e]=new E(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){I[e]=new E(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){I[e]=new E(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){I[e]=new E(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){I[e]=new E(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){I[e]=new E(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){I[e]=new E(e,5,!1,e.toLowerCase(),null,!1,!1)});var j=/[\-:]([a-z])/g;function B(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(j,B);I[t]=new E(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(j,B);I[t]=new E(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(j,B);I[t]=new E(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){I[e]=new E(e,1,!1,e.toLowerCase(),null,!1,!1)}),I.xlinkHref=new E("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){I[e]=new E(e,1,!1,e.toLowerCase(),null,!0,!0)});function L(e,t,n,o){var i=I.hasOwnProperty(t)?I[t]:null;(i!==null?i.type!==0:o||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(R(t,n,i,o)&&(n=null),o||i===null?C(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,o=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,o?e.setAttributeNS(o,t,n):e.setAttribute(t,n))))}var F=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,M=Symbol.for("react.element"),z=Symbol.for("react.portal"),U=Symbol.for("react.fragment"),J=Symbol.for("react.strict_mode"),X=Symbol.for("react.profiler"),oe=Symbol.for("react.provider"),me=Symbol.for("react.context"),xe=Symbol.for("react.forward_ref"),ye=Symbol.for("react.suspense"),Y=Symbol.for("react.suspense_list"),Z=Symbol.for("react.memo"),le=Symbol.for("react.lazy"),te=Symbol.for("react.offscreen"),_=Symbol.iterator;function b(e){return e===null||typeof e!="object"?null:(e=_&&e[_]||e["@@iterator"],typeof e=="function"?e:null)}var G=Object.assign,P;function Q(e){if(P===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);P=t&&t[1]||""}return`
`+P+e}var ue=!1;function ce(e,t){if(!e||ue)return"";ue=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(T){var o=T}Reflect.construct(e,[],t)}else{try{t.call()}catch(T){o=T}e.call(t.prototype)}else{try{throw Error()}catch(T){o=T}e()}}catch(T){if(T&&o&&typeof T.stack=="string"){for(var i=T.stack.split(`
`),a=o.stack.split(`
`),p=i.length-1,x=a.length-1;1<=p&&0<=x&&i[p]!==a[x];)x--;for(;1<=p&&0<=x;p--,x--)if(i[p]!==a[x]){if(p!==1||x!==1)do if(p--,x--,0>x||i[p]!==a[x]){var k=`
`+i[p].replace(" at new "," at ");return e.displayName&&k.includes("<anonymous>")&&(k=k.replace("<anonymous>",e.displayName)),k}while(1<=p&&0<=x);break}}}finally{ue=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Q(e):""}function he(e){switch(e.tag){case 5:return Q(e.type);case 16:return Q("Lazy");case 13:return Q("Suspense");case 19:return Q("SuspenseList");case 0:case 2:case 15:return e=ce(e.type,!1),e;case 11:return e=ce(e.type.render,!1),e;case 1:return e=ce(e.type,!0),e;default:return""}}function fe(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case U:return"Fragment";case z:return"Portal";case X:return"Profiler";case J:return"StrictMode";case ye:return"Suspense";case Y:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case me:return(e.displayName||"Context")+".Consumer";case oe:return(e._context.displayName||"Context")+".Provider";case xe:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Z:return t=e.displayName||null,t!==null?t:fe(e.type)||"Memo";case le:t=e._payload,e=e._init;try{return fe(e(t))}catch{}}return null}function ve(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return fe(t);case 8:return t===J?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function de(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function we(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ee(e){var t=we(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),o=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(p){o=""+p,a.call(this,p)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return o},setValue:function(p){o=""+p},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ke(e){e._valueTracker||(e._valueTracker=Ee(e))}function ie(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),o="";return e&&(o=we(e)?e.checked?"true":"false":e.value),e=o,e!==n?(t.setValue(e),!0):!1}function ge(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Me(e,t){var n=t.checked;return G({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ge(e,t){var n=t.defaultValue==null?"":t.defaultValue,o=t.checked!=null?t.checked:t.defaultChecked;n=de(t.value!=null?t.value:n),e._wrapperState={initialChecked:o,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Mt(e,t){t=t.checked,t!=null&&L(e,"checked",t,!1)}function rn(e,t){Mt(e,t);var n=de(t.value),o=t.type;if(n!=null)o==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(o==="submit"||o==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?pt(e,t.type,n):t.hasOwnProperty("defaultValue")&&pt(e,t.type,de(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Yn(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var o=t.type;if(!(o!=="submit"&&o!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function pt(e,t,n){(t!=="number"||ge(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var on=Array.isArray;function Ct(e,t,n,o){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&o&&(e[n].defaultSelected=!0)}else{for(n=""+de(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,o&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function ht(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(s(91));return G({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Va(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(s(92));if(on(n)){if(1<n.length)throw Error(s(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:de(n)}}function Wa(e,t){var n=de(t.value),o=de(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),o!=null&&(e.defaultValue=""+o)}function Ha(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ya(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ii(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ya(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var No,Ka=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,o,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,o,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(No=No||document.createElement("div"),No.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=No.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Or(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Tr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Wp=["Webkit","ms","Moz","O"];Object.keys(Tr).forEach(function(e){Wp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Tr[t]=Tr[e]})});function Ga(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Tr.hasOwnProperty(e)&&Tr[e]?(""+t).trim():t+"px"}function ba(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var o=n.indexOf("--")===0,i=Ga(n,t[n],o);n==="float"&&(n="cssFloat"),o?e.setProperty(n,i):e[n]=i}}var Hp=G({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function si(e,t){if(t){if(Hp[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(s(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(s(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(s(61))}if(t.style!=null&&typeof t.style!="object")throw Error(s(62))}}function ai(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ui=null;function ci(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var fi=null,Kn=null,Gn=null;function Ja(e){if(e=eo(e)){if(typeof fi!="function")throw Error(s(280));var t=e.stateNode;t&&(t=Jo(t),fi(e.stateNode,e.type,t))}}function Xa(e){Kn?Gn?Gn.push(e):Gn=[e]:Kn=e}function $a(){if(Kn){var e=Kn,t=Gn;if(Gn=Kn=null,Ja(e),t)for(e=0;e<t.length;e++)Ja(t[e])}}function Za(e,t){return e(t)}function qa(){}var di=!1;function eu(e,t,n){if(di)return e(t,n);di=!0;try{return Za(e,t,n)}finally{di=!1,(Kn!==null||Gn!==null)&&(qa(),$a())}}function Mr(e,t){var n=e.stateNode;if(n===null)return null;var o=Jo(n);if(o===null)return null;n=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(e=e.type,o=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!o;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(s(231,t,typeof n));return n}var pi=!1;if(h)try{var jr={};Object.defineProperty(jr,"passive",{get:function(){pi=!0}}),window.addEventListener("test",jr,jr),window.removeEventListener("test",jr,jr)}catch{pi=!1}function Yp(e,t,n,o,i,a,p,x,k){var T=Array.prototype.slice.call(arguments,3);try{t.apply(n,T)}catch(W){this.onError(W)}}var Lr=!1,Po=null,Ro=!1,hi=null,Kp={onError:function(e){Lr=!0,Po=e}};function Gp(e,t,n,o,i,a,p,x,k){Lr=!1,Po=null,Yp.apply(Kp,arguments)}function bp(e,t,n,o,i,a,p,x,k){if(Gp.apply(this,arguments),Lr){if(Lr){var T=Po;Lr=!1,Po=null}else throw Error(s(198));Ro||(Ro=!0,hi=T)}}function Dn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function tu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function nu(e){if(Dn(e)!==e)throw Error(s(188))}function Jp(e){var t=e.alternate;if(!t){if(t=Dn(e),t===null)throw Error(s(188));return t!==e?null:e}for(var n=e,o=t;;){var i=n.return;if(i===null)break;var a=i.alternate;if(a===null){if(o=i.return,o!==null){n=o;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===n)return nu(i),e;if(a===o)return nu(i),t;a=a.sibling}throw Error(s(188))}if(n.return!==o.return)n=i,o=a;else{for(var p=!1,x=i.child;x;){if(x===n){p=!0,n=i,o=a;break}if(x===o){p=!0,o=i,n=a;break}x=x.sibling}if(!p){for(x=a.child;x;){if(x===n){p=!0,n=a,o=i;break}if(x===o){p=!0,o=a,n=i;break}x=x.sibling}if(!p)throw Error(s(189))}}if(n.alternate!==o)throw Error(s(190))}if(n.tag!==3)throw Error(s(188));return n.stateNode.current===n?e:t}function ru(e){return e=Jp(e),e!==null?ou(e):null}function ou(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ou(e);if(t!==null)return t;e=e.sibling}return null}var lu=l.unstable_scheduleCallback,iu=l.unstable_cancelCallback,Xp=l.unstable_shouldYield,$p=l.unstable_requestPaint,je=l.unstable_now,Zp=l.unstable_getCurrentPriorityLevel,mi=l.unstable_ImmediatePriority,su=l.unstable_UserBlockingPriority,Io=l.unstable_NormalPriority,qp=l.unstable_LowPriority,au=l.unstable_IdlePriority,Do=null,jt=null;function eh(e){if(jt&&typeof jt.onCommitFiberRoot=="function")try{jt.onCommitFiberRoot(Do,e,void 0,(e.current.flags&128)===128)}catch{}}var St=Math.clz32?Math.clz32:rh,th=Math.log,nh=Math.LN2;function rh(e){return e>>>=0,e===0?32:31-(th(e)/nh|0)|0}var Oo=64,To=4194304;function Br(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Mo(e,t){var n=e.pendingLanes;if(n===0)return 0;var o=0,i=e.suspendedLanes,a=e.pingedLanes,p=n&268435455;if(p!==0){var x=p&~i;x!==0?o=Br(x):(a&=p,a!==0&&(o=Br(a)))}else p=n&~i,p!==0?o=Br(p):a!==0&&(o=Br(a));if(o===0)return 0;if(t!==0&&t!==o&&(t&i)===0&&(i=o&-o,a=t&-t,i>=a||i===16&&(a&4194240)!==0))return t;if((o&4)!==0&&(o|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=o;0<t;)n=31-St(t),i=1<<n,o|=e[n],t&=~i;return o}function oh(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function lh(e,t){for(var n=e.suspendedLanes,o=e.pingedLanes,i=e.expirationTimes,a=e.pendingLanes;0<a;){var p=31-St(a),x=1<<p,k=i[p];k===-1?((x&n)===0||(x&o)!==0)&&(i[p]=oh(x,t)):k<=t&&(e.expiredLanes|=x),a&=~x}}function gi(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function uu(){var e=Oo;return Oo<<=1,(Oo&4194240)===0&&(Oo=64),e}function vi(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Fr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-St(t),e[t]=n}function ih(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var o=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-St(n),a=1<<i;t[i]=0,o[i]=-1,e[i]=-1,n&=~a}}function yi(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var o=31-St(n),i=1<<o;i&t|e[o]&t&&(e[o]|=t),n&=~i}}var ke=0;function cu(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var fu,Ai,du,pu,hu,wi=!1,jo=[],ln=null,sn=null,an=null,_r=new Map,zr=new Map,un=[],sh="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function mu(e,t){switch(e){case"focusin":case"focusout":ln=null;break;case"dragenter":case"dragleave":sn=null;break;case"mouseover":case"mouseout":an=null;break;case"pointerover":case"pointerout":_r.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":zr.delete(t.pointerId)}}function Ur(e,t,n,o,i,a){return e===null||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:o,nativeEvent:a,targetContainers:[i]},t!==null&&(t=eo(t),t!==null&&Ai(t)),e):(e.eventSystemFlags|=o,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function ah(e,t,n,o,i){switch(t){case"focusin":return ln=Ur(ln,e,t,n,o,i),!0;case"dragenter":return sn=Ur(sn,e,t,n,o,i),!0;case"mouseover":return an=Ur(an,e,t,n,o,i),!0;case"pointerover":var a=i.pointerId;return _r.set(a,Ur(_r.get(a)||null,e,t,n,o,i)),!0;case"gotpointercapture":return a=i.pointerId,zr.set(a,Ur(zr.get(a)||null,e,t,n,o,i)),!0}return!1}function gu(e){var t=On(e.target);if(t!==null){var n=Dn(t);if(n!==null){if(t=n.tag,t===13){if(t=tu(n),t!==null){e.blockedOn=t,hu(e.priority,function(){du(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Lo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ci(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var o=new n.constructor(n.type,n);ui=o,n.target.dispatchEvent(o),ui=null}else return t=eo(n),t!==null&&Ai(t),e.blockedOn=n,!1;t.shift()}return!0}function vu(e,t,n){Lo(e)&&n.delete(t)}function uh(){wi=!1,ln!==null&&Lo(ln)&&(ln=null),sn!==null&&Lo(sn)&&(sn=null),an!==null&&Lo(an)&&(an=null),_r.forEach(vu),zr.forEach(vu)}function Qr(e,t){e.blockedOn===t&&(e.blockedOn=null,wi||(wi=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,uh)))}function Vr(e){function t(i){return Qr(i,e)}if(0<jo.length){Qr(jo[0],e);for(var n=1;n<jo.length;n++){var o=jo[n];o.blockedOn===e&&(o.blockedOn=null)}}for(ln!==null&&Qr(ln,e),sn!==null&&Qr(sn,e),an!==null&&Qr(an,e),_r.forEach(t),zr.forEach(t),n=0;n<un.length;n++)o=un[n],o.blockedOn===e&&(o.blockedOn=null);for(;0<un.length&&(n=un[0],n.blockedOn===null);)gu(n),n.blockedOn===null&&un.shift()}var bn=F.ReactCurrentBatchConfig,Bo=!0;function ch(e,t,n,o){var i=ke,a=bn.transition;bn.transition=null;try{ke=1,xi(e,t,n,o)}finally{ke=i,bn.transition=a}}function fh(e,t,n,o){var i=ke,a=bn.transition;bn.transition=null;try{ke=4,xi(e,t,n,o)}finally{ke=i,bn.transition=a}}function xi(e,t,n,o){if(Bo){var i=Ci(e,t,n,o);if(i===null)zi(e,t,o,Fo,n),mu(e,o);else if(ah(i,e,t,n,o))o.stopPropagation();else if(mu(e,o),t&4&&-1<sh.indexOf(e)){for(;i!==null;){var a=eo(i);if(a!==null&&fu(a),a=Ci(e,t,n,o),a===null&&zi(e,t,o,Fo,n),a===i)break;i=a}i!==null&&o.stopPropagation()}else zi(e,t,o,null,n)}}var Fo=null;function Ci(e,t,n,o){if(Fo=null,e=ci(o),e=On(e),e!==null)if(t=Dn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=tu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Fo=e,null}function yu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Zp()){case mi:return 1;case su:return 4;case Io:case qp:return 16;case au:return 536870912;default:return 16}default:return 16}}var cn=null,Si=null,_o=null;function Au(){if(_o)return _o;var e,t=Si,n=t.length,o,i="value"in cn?cn.value:cn.textContent,a=i.length;for(e=0;e<n&&t[e]===i[e];e++);var p=n-e;for(o=1;o<=p&&t[n-o]===i[a-o];o++);return _o=i.slice(e,1<o?1-o:void 0)}function zo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Uo(){return!0}function wu(){return!1}function st(e){function t(n,o,i,a,p){this._reactName=n,this._targetInst=i,this.type=o,this.nativeEvent=a,this.target=p,this.currentTarget=null;for(var x in e)e.hasOwnProperty(x)&&(n=e[x],this[x]=n?n(a):a[x]);return this.isDefaultPrevented=(a.defaultPrevented!=null?a.defaultPrevented:a.returnValue===!1)?Uo:wu,this.isPropagationStopped=wu,this}return G(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Uo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Uo)},persist:function(){},isPersistent:Uo}),t}var Jn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ei=st(Jn),Wr=G({},Jn,{view:0,detail:0}),dh=st(Wr),ki,Ni,Hr,Qo=G({},Wr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ri,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Hr&&(Hr&&e.type==="mousemove"?(ki=e.screenX-Hr.screenX,Ni=e.screenY-Hr.screenY):Ni=ki=0,Hr=e),ki)},movementY:function(e){return"movementY"in e?e.movementY:Ni}}),xu=st(Qo),ph=G({},Qo,{dataTransfer:0}),hh=st(ph),mh=G({},Wr,{relatedTarget:0}),Pi=st(mh),gh=G({},Jn,{animationName:0,elapsedTime:0,pseudoElement:0}),vh=st(gh),yh=G({},Jn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ah=st(yh),wh=G({},Jn,{data:0}),Cu=st(wh),xh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ch={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sh={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Eh(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Sh[e])?!!t[e]:!1}function Ri(){return Eh}var kh=G({},Wr,{key:function(e){if(e.key){var t=xh[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=zo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ch[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ri,charCode:function(e){return e.type==="keypress"?zo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?zo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Nh=st(kh),Ph=G({},Qo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Su=st(Ph),Rh=G({},Wr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ri}),Ih=st(Rh),Dh=G({},Jn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Oh=st(Dh),Th=G({},Qo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mh=st(Th),jh=[9,13,27,32],Ii=h&&"CompositionEvent"in window,Yr=null;h&&"documentMode"in document&&(Yr=document.documentMode);var Lh=h&&"TextEvent"in window&&!Yr,Eu=h&&(!Ii||Yr&&8<Yr&&11>=Yr),ku=" ",Nu=!1;function Pu(e,t){switch(e){case"keyup":return jh.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ru(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Xn=!1;function Bh(e,t){switch(e){case"compositionend":return Ru(t);case"keypress":return t.which!==32?null:(Nu=!0,ku);case"textInput":return e=t.data,e===ku&&Nu?null:e;default:return null}}function Fh(e,t){if(Xn)return e==="compositionend"||!Ii&&Pu(e,t)?(e=Au(),_o=Si=cn=null,Xn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Eu&&t.locale!=="ko"?null:t.data;default:return null}}var _h={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Iu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!_h[e.type]:t==="textarea"}function Du(e,t,n,o){Xa(o),t=Ko(t,"onChange"),0<t.length&&(n=new Ei("onChange","change",null,n,o),e.push({event:n,listeners:t}))}var Kr=null,Gr=null;function zh(e){bu(e,0)}function Vo(e){var t=tr(e);if(ie(t))return e}function Uh(e,t){if(e==="change")return t}var Ou=!1;if(h){var Di;if(h){var Oi="oninput"in document;if(!Oi){var Tu=document.createElement("div");Tu.setAttribute("oninput","return;"),Oi=typeof Tu.oninput=="function"}Di=Oi}else Di=!1;Ou=Di&&(!document.documentMode||9<document.documentMode)}function Mu(){Kr&&(Kr.detachEvent("onpropertychange",ju),Gr=Kr=null)}function ju(e){if(e.propertyName==="value"&&Vo(Gr)){var t=[];Du(t,Gr,e,ci(e)),eu(zh,t)}}function Qh(e,t,n){e==="focusin"?(Mu(),Kr=t,Gr=n,Kr.attachEvent("onpropertychange",ju)):e==="focusout"&&Mu()}function Vh(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Vo(Gr)}function Wh(e,t){if(e==="click")return Vo(t)}function Hh(e,t){if(e==="input"||e==="change")return Vo(t)}function Yh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Et=typeof Object.is=="function"?Object.is:Yh;function br(e,t){if(Et(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;for(o=0;o<n.length;o++){var i=n[o];if(!m.call(t,i)||!Et(e[i],t[i]))return!1}return!0}function Lu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Bu(e,t){var n=Lu(e);e=0;for(var o;n;){if(n.nodeType===3){if(o=e+n.textContent.length,e<=t&&o>=t)return{node:n,offset:t-e};e=o}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Lu(n)}}function Fu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Fu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function _u(){for(var e=window,t=ge();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ge(e.document)}return t}function Ti(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Kh(e){var t=_u(),n=e.focusedElem,o=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Fu(n.ownerDocument.documentElement,n)){if(o!==null&&Ti(n)){if(t=o.start,e=o.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,a=Math.min(o.start,i);o=o.end===void 0?a:Math.min(o.end,i),!e.extend&&a>o&&(i=o,o=a,a=i),i=Bu(n,a);var p=Bu(n,o);i&&p&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==p.node||e.focusOffset!==p.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),a>o?(e.addRange(t),e.extend(p.node,p.offset)):(t.setEnd(p.node,p.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Gh=h&&"documentMode"in document&&11>=document.documentMode,$n=null,Mi=null,Jr=null,ji=!1;function zu(e,t,n){var o=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ji||$n==null||$n!==ge(o)||(o=$n,"selectionStart"in o&&Ti(o)?o={start:o.selectionStart,end:o.selectionEnd}:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection(),o={anchorNode:o.anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset}),Jr&&br(Jr,o)||(Jr=o,o=Ko(Mi,"onSelect"),0<o.length&&(t=new Ei("onSelect","select",null,t,n),e.push({event:t,listeners:o}),t.target=$n)))}function Wo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Zn={animationend:Wo("Animation","AnimationEnd"),animationiteration:Wo("Animation","AnimationIteration"),animationstart:Wo("Animation","AnimationStart"),transitionend:Wo("Transition","TransitionEnd")},Li={},Uu={};h&&(Uu=document.createElement("div").style,"AnimationEvent"in window||(delete Zn.animationend.animation,delete Zn.animationiteration.animation,delete Zn.animationstart.animation),"TransitionEvent"in window||delete Zn.transitionend.transition);function Ho(e){if(Li[e])return Li[e];if(!Zn[e])return e;var t=Zn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Uu)return Li[e]=t[n];return e}var Qu=Ho("animationend"),Vu=Ho("animationiteration"),Wu=Ho("animationstart"),Hu=Ho("transitionend"),Yu=new Map,Ku="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function fn(e,t){Yu.set(e,t),f(t,[e])}for(var Bi=0;Bi<Ku.length;Bi++){var Fi=Ku[Bi],bh=Fi.toLowerCase(),Jh=Fi[0].toUpperCase()+Fi.slice(1);fn(bh,"on"+Jh)}fn(Qu,"onAnimationEnd"),fn(Vu,"onAnimationIteration"),fn(Wu,"onAnimationStart"),fn("dblclick","onDoubleClick"),fn("focusin","onFocus"),fn("focusout","onBlur"),fn(Hu,"onTransitionEnd"),d("onMouseEnter",["mouseout","mouseover"]),d("onMouseLeave",["mouseout","mouseover"]),d("onPointerEnter",["pointerout","pointerover"]),d("onPointerLeave",["pointerout","pointerover"]),f("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),f("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),f("onBeforeInput",["compositionend","keypress","textInput","paste"]),f("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Xr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Xh=new Set("cancel close invalid load scroll toggle".split(" ").concat(Xr));function Gu(e,t,n){var o=e.type||"unknown-event";e.currentTarget=n,bp(o,t,void 0,e),e.currentTarget=null}function bu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var o=e[n],i=o.event;o=o.listeners;e:{var a=void 0;if(t)for(var p=o.length-1;0<=p;p--){var x=o[p],k=x.instance,T=x.currentTarget;if(x=x.listener,k!==a&&i.isPropagationStopped())break e;Gu(i,x,T),a=k}else for(p=0;p<o.length;p++){if(x=o[p],k=x.instance,T=x.currentTarget,x=x.listener,k!==a&&i.isPropagationStopped())break e;Gu(i,x,T),a=k}}}if(Ro)throw e=hi,Ro=!1,hi=null,e}function Pe(e,t){var n=t[Yi];n===void 0&&(n=t[Yi]=new Set);var o=e+"__bubble";n.has(o)||(Ju(t,e,2,!1),n.add(o))}function _i(e,t,n){var o=0;t&&(o|=4),Ju(n,e,o,t)}var Yo="_reactListening"+Math.random().toString(36).slice(2);function $r(e){if(!e[Yo]){e[Yo]=!0,u.forEach(function(n){n!=="selectionchange"&&(Xh.has(n)||_i(n,!1,e),_i(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Yo]||(t[Yo]=!0,_i("selectionchange",!1,t))}}function Ju(e,t,n,o){switch(yu(t)){case 1:var i=ch;break;case 4:i=fh;break;default:i=xi}n=i.bind(null,t,n,e),i=void 0,!pi||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),o?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function zi(e,t,n,o,i){var a=o;if((t&1)===0&&(t&2)===0&&o!==null)e:for(;;){if(o===null)return;var p=o.tag;if(p===3||p===4){var x=o.stateNode.containerInfo;if(x===i||x.nodeType===8&&x.parentNode===i)break;if(p===4)for(p=o.return;p!==null;){var k=p.tag;if((k===3||k===4)&&(k=p.stateNode.containerInfo,k===i||k.nodeType===8&&k.parentNode===i))return;p=p.return}for(;x!==null;){if(p=On(x),p===null)return;if(k=p.tag,k===5||k===6){o=a=p;continue e}x=x.parentNode}}o=o.return}eu(function(){var T=a,W=ci(n),H=[];e:{var V=Yu.get(e);if(V!==void 0){var $=Ei,ee=e;switch(e){case"keypress":if(zo(n)===0)break e;case"keydown":case"keyup":$=Nh;break;case"focusin":ee="focus",$=Pi;break;case"focusout":ee="blur",$=Pi;break;case"beforeblur":case"afterblur":$=Pi;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":$=xu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":$=hh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":$=Ih;break;case Qu:case Vu:case Wu:$=vh;break;case Hu:$=Oh;break;case"scroll":$=dh;break;case"wheel":$=Mh;break;case"copy":case"cut":case"paste":$=Ah;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":$=Su}var ne=(t&4)!==0,Le=!ne&&e==="scroll",D=ne?V!==null?V+"Capture":null:V;ne=[];for(var N=T,O;N!==null;){O=N;var K=O.stateNode;if(O.tag===5&&K!==null&&(O=K,D!==null&&(K=Mr(N,D),K!=null&&ne.push(Zr(N,K,O)))),Le)break;N=N.return}0<ne.length&&(V=new $(V,ee,null,n,W),H.push({event:V,listeners:ne}))}}if((t&7)===0){e:{if(V=e==="mouseover"||e==="pointerover",$=e==="mouseout"||e==="pointerout",V&&n!==ui&&(ee=n.relatedTarget||n.fromElement)&&(On(ee)||ee[Kt]))break e;if(($||V)&&(V=W.window===W?W:(V=W.ownerDocument)?V.defaultView||V.parentWindow:window,$?(ee=n.relatedTarget||n.toElement,$=T,ee=ee?On(ee):null,ee!==null&&(Le=Dn(ee),ee!==Le||ee.tag!==5&&ee.tag!==6)&&(ee=null)):($=null,ee=T),$!==ee)){if(ne=xu,K="onMouseLeave",D="onMouseEnter",N="mouse",(e==="pointerout"||e==="pointerover")&&(ne=Su,K="onPointerLeave",D="onPointerEnter",N="pointer"),Le=$==null?V:tr($),O=ee==null?V:tr(ee),V=new ne(K,N+"leave",$,n,W),V.target=Le,V.relatedTarget=O,K=null,On(W)===T&&(ne=new ne(D,N+"enter",ee,n,W),ne.target=O,ne.relatedTarget=Le,K=ne),Le=K,$&&ee)t:{for(ne=$,D=ee,N=0,O=ne;O;O=qn(O))N++;for(O=0,K=D;K;K=qn(K))O++;for(;0<N-O;)ne=qn(ne),N--;for(;0<O-N;)D=qn(D),O--;for(;N--;){if(ne===D||D!==null&&ne===D.alternate)break t;ne=qn(ne),D=qn(D)}ne=null}else ne=null;$!==null&&Xu(H,V,$,ne,!1),ee!==null&&Le!==null&&Xu(H,Le,ee,ne,!0)}}e:{if(V=T?tr(T):window,$=V.nodeName&&V.nodeName.toLowerCase(),$==="select"||$==="input"&&V.type==="file")var re=Uh;else if(Iu(V))if(Ou)re=Hh;else{re=Vh;var se=Qh}else($=V.nodeName)&&$.toLowerCase()==="input"&&(V.type==="checkbox"||V.type==="radio")&&(re=Wh);if(re&&(re=re(e,T))){Du(H,re,n,W);break e}se&&se(e,V,T),e==="focusout"&&(se=V._wrapperState)&&se.controlled&&V.type==="number"&&pt(V,"number",V.value)}switch(se=T?tr(T):window,e){case"focusin":(Iu(se)||se.contentEditable==="true")&&($n=se,Mi=T,Jr=null);break;case"focusout":Jr=Mi=$n=null;break;case"mousedown":ji=!0;break;case"contextmenu":case"mouseup":case"dragend":ji=!1,zu(H,n,W);break;case"selectionchange":if(Gh)break;case"keydown":case"keyup":zu(H,n,W)}var ae;if(Ii)e:{switch(e){case"compositionstart":var pe="onCompositionStart";break e;case"compositionend":pe="onCompositionEnd";break e;case"compositionupdate":pe="onCompositionUpdate";break e}pe=void 0}else Xn?Pu(e,n)&&(pe="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(pe="onCompositionStart");pe&&(Eu&&n.locale!=="ko"&&(Xn||pe!=="onCompositionStart"?pe==="onCompositionEnd"&&Xn&&(ae=Au()):(cn=W,Si="value"in cn?cn.value:cn.textContent,Xn=!0)),se=Ko(T,pe),0<se.length&&(pe=new Cu(pe,e,null,n,W),H.push({event:pe,listeners:se}),ae?pe.data=ae:(ae=Ru(n),ae!==null&&(pe.data=ae)))),(ae=Lh?Bh(e,n):Fh(e,n))&&(T=Ko(T,"onBeforeInput"),0<T.length&&(W=new Cu("onBeforeInput","beforeinput",null,n,W),H.push({event:W,listeners:T}),W.data=ae))}bu(H,t)})}function Zr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ko(e,t){for(var n=t+"Capture",o=[];e!==null;){var i=e,a=i.stateNode;i.tag===5&&a!==null&&(i=a,a=Mr(e,n),a!=null&&o.unshift(Zr(e,a,i)),a=Mr(e,t),a!=null&&o.push(Zr(e,a,i))),e=e.return}return o}function qn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Xu(e,t,n,o,i){for(var a=t._reactName,p=[];n!==null&&n!==o;){var x=n,k=x.alternate,T=x.stateNode;if(k!==null&&k===o)break;x.tag===5&&T!==null&&(x=T,i?(k=Mr(n,a),k!=null&&p.unshift(Zr(n,k,x))):i||(k=Mr(n,a),k!=null&&p.push(Zr(n,k,x)))),n=n.return}p.length!==0&&e.push({event:t,listeners:p})}var $h=/\r\n?/g,Zh=/\u0000|\uFFFD/g;function $u(e){return(typeof e=="string"?e:""+e).replace($h,`
`).replace(Zh,"")}function Go(e,t,n){if(t=$u(t),$u(e)!==t&&n)throw Error(s(425))}function bo(){}var Ui=null,Qi=null;function Vi(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Wi=typeof setTimeout=="function"?setTimeout:void 0,qh=typeof clearTimeout=="function"?clearTimeout:void 0,Zu=typeof Promise=="function"?Promise:void 0,em=typeof queueMicrotask=="function"?queueMicrotask:typeof Zu<"u"?function(e){return Zu.resolve(null).then(e).catch(tm)}:Wi;function tm(e){setTimeout(function(){throw e})}function Hi(e,t){var n=t,o=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(o===0){e.removeChild(i),Vr(t);return}o--}else n!=="$"&&n!=="$?"&&n!=="$!"||o++;n=i}while(n);Vr(t)}function dn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function qu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var er=Math.random().toString(36).slice(2),Lt="__reactFiber$"+er,qr="__reactProps$"+er,Kt="__reactContainer$"+er,Yi="__reactEvents$"+er,nm="__reactListeners$"+er,rm="__reactHandles$"+er;function On(e){var t=e[Lt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Kt]||n[Lt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=qu(e);e!==null;){if(n=e[Lt])return n;e=qu(e)}return t}e=n,n=e.parentNode}return null}function eo(e){return e=e[Lt]||e[Kt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function tr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(s(33))}function Jo(e){return e[qr]||null}var Ki=[],nr=-1;function pn(e){return{current:e}}function Re(e){0>nr||(e.current=Ki[nr],Ki[nr]=null,nr--)}function Ne(e,t){nr++,Ki[nr]=e.current,e.current=t}var hn={},be=pn(hn),et=pn(!1),Tn=hn;function rr(e,t){var n=e.type.contextTypes;if(!n)return hn;var o=e.stateNode;if(o&&o.__reactInternalMemoizedUnmaskedChildContext===t)return o.__reactInternalMemoizedMaskedChildContext;var i={},a;for(a in n)i[a]=t[a];return o&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function tt(e){return e=e.childContextTypes,e!=null}function Xo(){Re(et),Re(be)}function ec(e,t,n){if(be.current!==hn)throw Error(s(168));Ne(be,t),Ne(et,n)}function tc(e,t,n){var o=e.stateNode;if(t=t.childContextTypes,typeof o.getChildContext!="function")return n;o=o.getChildContext();for(var i in o)if(!(i in t))throw Error(s(108,ve(e)||"Unknown",i));return G({},n,o)}function $o(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||hn,Tn=be.current,Ne(be,e),Ne(et,et.current),!0}function nc(e,t,n){var o=e.stateNode;if(!o)throw Error(s(169));n?(e=tc(e,t,Tn),o.__reactInternalMemoizedMergedChildContext=e,Re(et),Re(be),Ne(be,e)):Re(et),Ne(et,n)}var Gt=null,Zo=!1,Gi=!1;function rc(e){Gt===null?Gt=[e]:Gt.push(e)}function om(e){Zo=!0,rc(e)}function mn(){if(!Gi&&Gt!==null){Gi=!0;var e=0,t=ke;try{var n=Gt;for(ke=1;e<n.length;e++){var o=n[e];do o=o(!0);while(o!==null)}Gt=null,Zo=!1}catch(i){throw Gt!==null&&(Gt=Gt.slice(e+1)),lu(mi,mn),i}finally{ke=t,Gi=!1}}return null}var or=[],lr=0,qo=null,el=0,mt=[],gt=0,Mn=null,bt=1,Jt="";function jn(e,t){or[lr++]=el,or[lr++]=qo,qo=e,el=t}function oc(e,t,n){mt[gt++]=bt,mt[gt++]=Jt,mt[gt++]=Mn,Mn=e;var o=bt;e=Jt;var i=32-St(o)-1;o&=~(1<<i),n+=1;var a=32-St(t)+i;if(30<a){var p=i-i%5;a=(o&(1<<p)-1).toString(32),o>>=p,i-=p,bt=1<<32-St(t)+i|n<<i|o,Jt=a+e}else bt=1<<a|n<<i|o,Jt=e}function bi(e){e.return!==null&&(jn(e,1),oc(e,1,0))}function Ji(e){for(;e===qo;)qo=or[--lr],or[lr]=null,el=or[--lr],or[lr]=null;for(;e===Mn;)Mn=mt[--gt],mt[gt]=null,Jt=mt[--gt],mt[gt]=null,bt=mt[--gt],mt[gt]=null}var at=null,ut=null,Ie=!1,kt=null;function lc(e,t){var n=wt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ic(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,at=e,ut=dn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,at=e,ut=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Mn!==null?{id:bt,overflow:Jt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=wt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,at=e,ut=null,!0):!1;default:return!1}}function Xi(e){return(e.mode&1)!==0&&(e.flags&128)===0}function $i(e){if(Ie){var t=ut;if(t){var n=t;if(!ic(e,t)){if(Xi(e))throw Error(s(418));t=dn(n.nextSibling);var o=at;t&&ic(e,t)?lc(o,n):(e.flags=e.flags&-4097|2,Ie=!1,at=e)}}else{if(Xi(e))throw Error(s(418));e.flags=e.flags&-4097|2,Ie=!1,at=e}}}function sc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;at=e}function tl(e){if(e!==at)return!1;if(!Ie)return sc(e),Ie=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Vi(e.type,e.memoizedProps)),t&&(t=ut)){if(Xi(e))throw ac(),Error(s(418));for(;t;)lc(e,t),t=dn(t.nextSibling)}if(sc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ut=dn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ut=null}}else ut=at?dn(e.stateNode.nextSibling):null;return!0}function ac(){for(var e=ut;e;)e=dn(e.nextSibling)}function ir(){ut=at=null,Ie=!1}function Zi(e){kt===null?kt=[e]:kt.push(e)}var lm=F.ReactCurrentBatchConfig;function to(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(s(309));var o=n.stateNode}if(!o)throw Error(s(147,e));var i=o,a=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===a?t.ref:(t=function(p){var x=i.refs;p===null?delete x[a]:x[a]=p},t._stringRef=a,t)}if(typeof e!="string")throw Error(s(284));if(!n._owner)throw Error(s(290,e))}return e}function nl(e,t){throw e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function uc(e){var t=e._init;return t(e._payload)}function cc(e){function t(D,N){if(e){var O=D.deletions;O===null?(D.deletions=[N],D.flags|=16):O.push(N)}}function n(D,N){if(!e)return null;for(;N!==null;)t(D,N),N=N.sibling;return null}function o(D,N){for(D=new Map;N!==null;)N.key!==null?D.set(N.key,N):D.set(N.index,N),N=N.sibling;return D}function i(D,N){return D=Sn(D,N),D.index=0,D.sibling=null,D}function a(D,N,O){return D.index=O,e?(O=D.alternate,O!==null?(O=O.index,O<N?(D.flags|=2,N):O):(D.flags|=2,N)):(D.flags|=1048576,N)}function p(D){return e&&D.alternate===null&&(D.flags|=2),D}function x(D,N,O,K){return N===null||N.tag!==6?(N=Ws(O,D.mode,K),N.return=D,N):(N=i(N,O),N.return=D,N)}function k(D,N,O,K){var re=O.type;return re===U?W(D,N,O.props.children,K,O.key):N!==null&&(N.elementType===re||typeof re=="object"&&re!==null&&re.$$typeof===le&&uc(re)===N.type)?(K=i(N,O.props),K.ref=to(D,N,O),K.return=D,K):(K=Nl(O.type,O.key,O.props,null,D.mode,K),K.ref=to(D,N,O),K.return=D,K)}function T(D,N,O,K){return N===null||N.tag!==4||N.stateNode.containerInfo!==O.containerInfo||N.stateNode.implementation!==O.implementation?(N=Hs(O,D.mode,K),N.return=D,N):(N=i(N,O.children||[]),N.return=D,N)}function W(D,N,O,K,re){return N===null||N.tag!==7?(N=Vn(O,D.mode,K,re),N.return=D,N):(N=i(N,O),N.return=D,N)}function H(D,N,O){if(typeof N=="string"&&N!==""||typeof N=="number")return N=Ws(""+N,D.mode,O),N.return=D,N;if(typeof N=="object"&&N!==null){switch(N.$$typeof){case M:return O=Nl(N.type,N.key,N.props,null,D.mode,O),O.ref=to(D,null,N),O.return=D,O;case z:return N=Hs(N,D.mode,O),N.return=D,N;case le:var K=N._init;return H(D,K(N._payload),O)}if(on(N)||b(N))return N=Vn(N,D.mode,O,null),N.return=D,N;nl(D,N)}return null}function V(D,N,O,K){var re=N!==null?N.key:null;if(typeof O=="string"&&O!==""||typeof O=="number")return re!==null?null:x(D,N,""+O,K);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case M:return O.key===re?k(D,N,O,K):null;case z:return O.key===re?T(D,N,O,K):null;case le:return re=O._init,V(D,N,re(O._payload),K)}if(on(O)||b(O))return re!==null?null:W(D,N,O,K,null);nl(D,O)}return null}function $(D,N,O,K,re){if(typeof K=="string"&&K!==""||typeof K=="number")return D=D.get(O)||null,x(N,D,""+K,re);if(typeof K=="object"&&K!==null){switch(K.$$typeof){case M:return D=D.get(K.key===null?O:K.key)||null,k(N,D,K,re);case z:return D=D.get(K.key===null?O:K.key)||null,T(N,D,K,re);case le:var se=K._init;return $(D,N,O,se(K._payload),re)}if(on(K)||b(K))return D=D.get(O)||null,W(N,D,K,re,null);nl(N,K)}return null}function ee(D,N,O,K){for(var re=null,se=null,ae=N,pe=N=0,Qe=null;ae!==null&&pe<O.length;pe++){ae.index>pe?(Qe=ae,ae=null):Qe=ae.sibling;var Se=V(D,ae,O[pe],K);if(Se===null){ae===null&&(ae=Qe);break}e&&ae&&Se.alternate===null&&t(D,ae),N=a(Se,N,pe),se===null?re=Se:se.sibling=Se,se=Se,ae=Qe}if(pe===O.length)return n(D,ae),Ie&&jn(D,pe),re;if(ae===null){for(;pe<O.length;pe++)ae=H(D,O[pe],K),ae!==null&&(N=a(ae,N,pe),se===null?re=ae:se.sibling=ae,se=ae);return Ie&&jn(D,pe),re}for(ae=o(D,ae);pe<O.length;pe++)Qe=$(ae,D,pe,O[pe],K),Qe!==null&&(e&&Qe.alternate!==null&&ae.delete(Qe.key===null?pe:Qe.key),N=a(Qe,N,pe),se===null?re=Qe:se.sibling=Qe,se=Qe);return e&&ae.forEach(function(En){return t(D,En)}),Ie&&jn(D,pe),re}function ne(D,N,O,K){var re=b(O);if(typeof re!="function")throw Error(s(150));if(O=re.call(O),O==null)throw Error(s(151));for(var se=re=null,ae=N,pe=N=0,Qe=null,Se=O.next();ae!==null&&!Se.done;pe++,Se=O.next()){ae.index>pe?(Qe=ae,ae=null):Qe=ae.sibling;var En=V(D,ae,Se.value,K);if(En===null){ae===null&&(ae=Qe);break}e&&ae&&En.alternate===null&&t(D,ae),N=a(En,N,pe),se===null?re=En:se.sibling=En,se=En,ae=Qe}if(Se.done)return n(D,ae),Ie&&jn(D,pe),re;if(ae===null){for(;!Se.done;pe++,Se=O.next())Se=H(D,Se.value,K),Se!==null&&(N=a(Se,N,pe),se===null?re=Se:se.sibling=Se,se=Se);return Ie&&jn(D,pe),re}for(ae=o(D,ae);!Se.done;pe++,Se=O.next())Se=$(ae,D,pe,Se.value,K),Se!==null&&(e&&Se.alternate!==null&&ae.delete(Se.key===null?pe:Se.key),N=a(Se,N,pe),se===null?re=Se:se.sibling=Se,se=Se);return e&&ae.forEach(function(_m){return t(D,_m)}),Ie&&jn(D,pe),re}function Le(D,N,O,K){if(typeof O=="object"&&O!==null&&O.type===U&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case M:e:{for(var re=O.key,se=N;se!==null;){if(se.key===re){if(re=O.type,re===U){if(se.tag===7){n(D,se.sibling),N=i(se,O.props.children),N.return=D,D=N;break e}}else if(se.elementType===re||typeof re=="object"&&re!==null&&re.$$typeof===le&&uc(re)===se.type){n(D,se.sibling),N=i(se,O.props),N.ref=to(D,se,O),N.return=D,D=N;break e}n(D,se);break}else t(D,se);se=se.sibling}O.type===U?(N=Vn(O.props.children,D.mode,K,O.key),N.return=D,D=N):(K=Nl(O.type,O.key,O.props,null,D.mode,K),K.ref=to(D,N,O),K.return=D,D=K)}return p(D);case z:e:{for(se=O.key;N!==null;){if(N.key===se)if(N.tag===4&&N.stateNode.containerInfo===O.containerInfo&&N.stateNode.implementation===O.implementation){n(D,N.sibling),N=i(N,O.children||[]),N.return=D,D=N;break e}else{n(D,N);break}else t(D,N);N=N.sibling}N=Hs(O,D.mode,K),N.return=D,D=N}return p(D);case le:return se=O._init,Le(D,N,se(O._payload),K)}if(on(O))return ee(D,N,O,K);if(b(O))return ne(D,N,O,K);nl(D,O)}return typeof O=="string"&&O!==""||typeof O=="number"?(O=""+O,N!==null&&N.tag===6?(n(D,N.sibling),N=i(N,O),N.return=D,D=N):(n(D,N),N=Ws(O,D.mode,K),N.return=D,D=N),p(D)):n(D,N)}return Le}var sr=cc(!0),fc=cc(!1),rl=pn(null),ol=null,ar=null,qi=null;function es(){qi=ar=ol=null}function ts(e){var t=rl.current;Re(rl),e._currentValue=t}function ns(e,t,n){for(;e!==null;){var o=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,o!==null&&(o.childLanes|=t)):o!==null&&(o.childLanes&t)!==t&&(o.childLanes|=t),e===n)break;e=e.return}}function ur(e,t){ol=e,qi=ar=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(nt=!0),e.firstContext=null)}function vt(e){var t=e._currentValue;if(qi!==e)if(e={context:e,memoizedValue:t,next:null},ar===null){if(ol===null)throw Error(s(308));ar=e,ol.dependencies={lanes:0,firstContext:e}}else ar=ar.next=e;return t}var Ln=null;function rs(e){Ln===null?Ln=[e]:Ln.push(e)}function dc(e,t,n,o){var i=t.interleaved;return i===null?(n.next=n,rs(t)):(n.next=i.next,i.next=n),t.interleaved=n,Xt(e,o)}function Xt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var gn=!1;function os(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function pc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function $t(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function vn(e,t,n){var o=e.updateQueue;if(o===null)return null;if(o=o.shared,(Ce&2)!==0){var i=o.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),o.pending=t,Xt(e,n)}return i=o.interleaved,i===null?(t.next=t,rs(o)):(t.next=i.next,i.next=t),o.interleaved=t,Xt(e,n)}function ll(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var o=t.lanes;o&=e.pendingLanes,n|=o,t.lanes=n,yi(e,n)}}function hc(e,t){var n=e.updateQueue,o=e.alternate;if(o!==null&&(o=o.updateQueue,n===o)){var i=null,a=null;if(n=n.firstBaseUpdate,n!==null){do{var p={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};a===null?i=a=p:a=a.next=p,n=n.next}while(n!==null);a===null?i=a=t:a=a.next=t}else i=a=t;n={baseState:o.baseState,firstBaseUpdate:i,lastBaseUpdate:a,shared:o.shared,effects:o.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function il(e,t,n,o){var i=e.updateQueue;gn=!1;var a=i.firstBaseUpdate,p=i.lastBaseUpdate,x=i.shared.pending;if(x!==null){i.shared.pending=null;var k=x,T=k.next;k.next=null,p===null?a=T:p.next=T,p=k;var W=e.alternate;W!==null&&(W=W.updateQueue,x=W.lastBaseUpdate,x!==p&&(x===null?W.firstBaseUpdate=T:x.next=T,W.lastBaseUpdate=k))}if(a!==null){var H=i.baseState;p=0,W=T=k=null,x=a;do{var V=x.lane,$=x.eventTime;if((o&V)===V){W!==null&&(W=W.next={eventTime:$,lane:0,tag:x.tag,payload:x.payload,callback:x.callback,next:null});e:{var ee=e,ne=x;switch(V=t,$=n,ne.tag){case 1:if(ee=ne.payload,typeof ee=="function"){H=ee.call($,H,V);break e}H=ee;break e;case 3:ee.flags=ee.flags&-65537|128;case 0:if(ee=ne.payload,V=typeof ee=="function"?ee.call($,H,V):ee,V==null)break e;H=G({},H,V);break e;case 2:gn=!0}}x.callback!==null&&x.lane!==0&&(e.flags|=64,V=i.effects,V===null?i.effects=[x]:V.push(x))}else $={eventTime:$,lane:V,tag:x.tag,payload:x.payload,callback:x.callback,next:null},W===null?(T=W=$,k=H):W=W.next=$,p|=V;if(x=x.next,x===null){if(x=i.shared.pending,x===null)break;V=x,x=V.next,V.next=null,i.lastBaseUpdate=V,i.shared.pending=null}}while(!0);if(W===null&&(k=H),i.baseState=k,i.firstBaseUpdate=T,i.lastBaseUpdate=W,t=i.shared.interleaved,t!==null){i=t;do p|=i.lane,i=i.next;while(i!==t)}else a===null&&(i.shared.lanes=0);_n|=p,e.lanes=p,e.memoizedState=H}}function mc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var o=e[t],i=o.callback;if(i!==null){if(o.callback=null,o=n,typeof i!="function")throw Error(s(191,i));i.call(o)}}}var no={},Bt=pn(no),ro=pn(no),oo=pn(no);function Bn(e){if(e===no)throw Error(s(174));return e}function ls(e,t){switch(Ne(oo,t),Ne(ro,e),Ne(Bt,no),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ii(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ii(t,e)}Re(Bt),Ne(Bt,t)}function cr(){Re(Bt),Re(ro),Re(oo)}function gc(e){Bn(oo.current);var t=Bn(Bt.current),n=ii(t,e.type);t!==n&&(Ne(ro,e),Ne(Bt,n))}function is(e){ro.current===e&&(Re(Bt),Re(ro))}var De=pn(0);function sl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ss=[];function as(){for(var e=0;e<ss.length;e++)ss[e]._workInProgressVersionPrimary=null;ss.length=0}var al=F.ReactCurrentDispatcher,us=F.ReactCurrentBatchConfig,Fn=0,Oe=null,Fe=null,ze=null,ul=!1,lo=!1,io=0,im=0;function Je(){throw Error(s(321))}function cs(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Et(e[n],t[n]))return!1;return!0}function fs(e,t,n,o,i,a){if(Fn=a,Oe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,al.current=e===null||e.memoizedState===null?cm:fm,e=n(o,i),lo){a=0;do{if(lo=!1,io=0,25<=a)throw Error(s(301));a+=1,ze=Fe=null,t.updateQueue=null,al.current=dm,e=n(o,i)}while(lo)}if(al.current=dl,t=Fe!==null&&Fe.next!==null,Fn=0,ze=Fe=Oe=null,ul=!1,t)throw Error(s(300));return e}function ds(){var e=io!==0;return io=0,e}function Ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ze===null?Oe.memoizedState=ze=e:ze=ze.next=e,ze}function yt(){if(Fe===null){var e=Oe.alternate;e=e!==null?e.memoizedState:null}else e=Fe.next;var t=ze===null?Oe.memoizedState:ze.next;if(t!==null)ze=t,Fe=e;else{if(e===null)throw Error(s(310));Fe=e,e={memoizedState:Fe.memoizedState,baseState:Fe.baseState,baseQueue:Fe.baseQueue,queue:Fe.queue,next:null},ze===null?Oe.memoizedState=ze=e:ze=ze.next=e}return ze}function so(e,t){return typeof t=="function"?t(e):t}function ps(e){var t=yt(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var o=Fe,i=o.baseQueue,a=n.pending;if(a!==null){if(i!==null){var p=i.next;i.next=a.next,a.next=p}o.baseQueue=i=a,n.pending=null}if(i!==null){a=i.next,o=o.baseState;var x=p=null,k=null,T=a;do{var W=T.lane;if((Fn&W)===W)k!==null&&(k=k.next={lane:0,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null}),o=T.hasEagerState?T.eagerState:e(o,T.action);else{var H={lane:W,action:T.action,hasEagerState:T.hasEagerState,eagerState:T.eagerState,next:null};k===null?(x=k=H,p=o):k=k.next=H,Oe.lanes|=W,_n|=W}T=T.next}while(T!==null&&T!==a);k===null?p=o:k.next=x,Et(o,t.memoizedState)||(nt=!0),t.memoizedState=o,t.baseState=p,t.baseQueue=k,n.lastRenderedState=o}if(e=n.interleaved,e!==null){i=e;do a=i.lane,Oe.lanes|=a,_n|=a,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function hs(e){var t=yt(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var o=n.dispatch,i=n.pending,a=t.memoizedState;if(i!==null){n.pending=null;var p=i=i.next;do a=e(a,p.action),p=p.next;while(p!==i);Et(a,t.memoizedState)||(nt=!0),t.memoizedState=a,t.baseQueue===null&&(t.baseState=a),n.lastRenderedState=a}return[a,o]}function vc(){}function yc(e,t){var n=Oe,o=yt(),i=t(),a=!Et(o.memoizedState,i);if(a&&(o.memoizedState=i,nt=!0),o=o.queue,ms(xc.bind(null,n,o,e),[e]),o.getSnapshot!==t||a||ze!==null&&ze.memoizedState.tag&1){if(n.flags|=2048,ao(9,wc.bind(null,n,o,i,t),void 0,null),Ue===null)throw Error(s(349));(Fn&30)!==0||Ac(n,t,i)}return i}function Ac(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Oe.updateQueue,t===null?(t={lastEffect:null,stores:null},Oe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function wc(e,t,n,o){t.value=n,t.getSnapshot=o,Cc(t)&&Sc(e)}function xc(e,t,n){return n(function(){Cc(t)&&Sc(e)})}function Cc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Et(e,n)}catch{return!0}}function Sc(e){var t=Xt(e,1);t!==null&&It(t,e,1,-1)}function Ec(e){var t=Ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:so,lastRenderedState:e},t.queue=e,e=e.dispatch=um.bind(null,Oe,e),[t.memoizedState,e]}function ao(e,t,n,o){return e={tag:e,create:t,destroy:n,deps:o,next:null},t=Oe.updateQueue,t===null?(t={lastEffect:null,stores:null},Oe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(o=n.next,n.next=e,e.next=o,t.lastEffect=e)),e}function kc(){return yt().memoizedState}function cl(e,t,n,o){var i=Ft();Oe.flags|=e,i.memoizedState=ao(1|t,n,void 0,o===void 0?null:o)}function fl(e,t,n,o){var i=yt();o=o===void 0?null:o;var a=void 0;if(Fe!==null){var p=Fe.memoizedState;if(a=p.destroy,o!==null&&cs(o,p.deps)){i.memoizedState=ao(t,n,a,o);return}}Oe.flags|=e,i.memoizedState=ao(1|t,n,a,o)}function Nc(e,t){return cl(8390656,8,e,t)}function ms(e,t){return fl(2048,8,e,t)}function Pc(e,t){return fl(4,2,e,t)}function Rc(e,t){return fl(4,4,e,t)}function Ic(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Dc(e,t,n){return n=n!=null?n.concat([e]):null,fl(4,4,Ic.bind(null,t,e),n)}function gs(){}function Oc(e,t){var n=yt();t=t===void 0?null:t;var o=n.memoizedState;return o!==null&&t!==null&&cs(t,o[1])?o[0]:(n.memoizedState=[e,t],e)}function Tc(e,t){var n=yt();t=t===void 0?null:t;var o=n.memoizedState;return o!==null&&t!==null&&cs(t,o[1])?o[0]:(e=e(),n.memoizedState=[e,t],e)}function Mc(e,t,n){return(Fn&21)===0?(e.baseState&&(e.baseState=!1,nt=!0),e.memoizedState=n):(Et(n,t)||(n=uu(),Oe.lanes|=n,_n|=n,e.baseState=!0),t)}function sm(e,t){var n=ke;ke=n!==0&&4>n?n:4,e(!0);var o=us.transition;us.transition={};try{e(!1),t()}finally{ke=n,us.transition=o}}function jc(){return yt().memoizedState}function am(e,t,n){var o=xn(e);if(n={lane:o,action:n,hasEagerState:!1,eagerState:null,next:null},Lc(e))Bc(t,n);else if(n=dc(e,t,n,o),n!==null){var i=qe();It(n,e,o,i),Fc(n,t,o)}}function um(e,t,n){var o=xn(e),i={lane:o,action:n,hasEagerState:!1,eagerState:null,next:null};if(Lc(e))Bc(t,i);else{var a=e.alternate;if(e.lanes===0&&(a===null||a.lanes===0)&&(a=t.lastRenderedReducer,a!==null))try{var p=t.lastRenderedState,x=a(p,n);if(i.hasEagerState=!0,i.eagerState=x,Et(x,p)){var k=t.interleaved;k===null?(i.next=i,rs(t)):(i.next=k.next,k.next=i),t.interleaved=i;return}}catch{}finally{}n=dc(e,t,i,o),n!==null&&(i=qe(),It(n,e,o,i),Fc(n,t,o))}}function Lc(e){var t=e.alternate;return e===Oe||t!==null&&t===Oe}function Bc(e,t){lo=ul=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Fc(e,t,n){if((n&4194240)!==0){var o=t.lanes;o&=e.pendingLanes,n|=o,t.lanes=n,yi(e,n)}}var dl={readContext:vt,useCallback:Je,useContext:Je,useEffect:Je,useImperativeHandle:Je,useInsertionEffect:Je,useLayoutEffect:Je,useMemo:Je,useReducer:Je,useRef:Je,useState:Je,useDebugValue:Je,useDeferredValue:Je,useTransition:Je,useMutableSource:Je,useSyncExternalStore:Je,useId:Je,unstable_isNewReconciler:!1},cm={readContext:vt,useCallback:function(e,t){return Ft().memoizedState=[e,t===void 0?null:t],e},useContext:vt,useEffect:Nc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,cl(4194308,4,Ic.bind(null,t,e),n)},useLayoutEffect:function(e,t){return cl(4194308,4,e,t)},useInsertionEffect:function(e,t){return cl(4,2,e,t)},useMemo:function(e,t){var n=Ft();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var o=Ft();return t=n!==void 0?n(t):t,o.memoizedState=o.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},o.queue=e,e=e.dispatch=am.bind(null,Oe,e),[o.memoizedState,e]},useRef:function(e){var t=Ft();return e={current:e},t.memoizedState=e},useState:Ec,useDebugValue:gs,useDeferredValue:function(e){return Ft().memoizedState=e},useTransition:function(){var e=Ec(!1),t=e[0];return e=sm.bind(null,e[1]),Ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var o=Oe,i=Ft();if(Ie){if(n===void 0)throw Error(s(407));n=n()}else{if(n=t(),Ue===null)throw Error(s(349));(Fn&30)!==0||Ac(o,t,n)}i.memoizedState=n;var a={value:n,getSnapshot:t};return i.queue=a,Nc(xc.bind(null,o,a,e),[e]),o.flags|=2048,ao(9,wc.bind(null,o,a,n,t),void 0,null),n},useId:function(){var e=Ft(),t=Ue.identifierPrefix;if(Ie){var n=Jt,o=bt;n=(o&~(1<<32-St(o)-1)).toString(32)+n,t=":"+t+"R"+n,n=io++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=im++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},fm={readContext:vt,useCallback:Oc,useContext:vt,useEffect:ms,useImperativeHandle:Dc,useInsertionEffect:Pc,useLayoutEffect:Rc,useMemo:Tc,useReducer:ps,useRef:kc,useState:function(){return ps(so)},useDebugValue:gs,useDeferredValue:function(e){var t=yt();return Mc(t,Fe.memoizedState,e)},useTransition:function(){var e=ps(so)[0],t=yt().memoizedState;return[e,t]},useMutableSource:vc,useSyncExternalStore:yc,useId:jc,unstable_isNewReconciler:!1},dm={readContext:vt,useCallback:Oc,useContext:vt,useEffect:ms,useImperativeHandle:Dc,useInsertionEffect:Pc,useLayoutEffect:Rc,useMemo:Tc,useReducer:hs,useRef:kc,useState:function(){return hs(so)},useDebugValue:gs,useDeferredValue:function(e){var t=yt();return Fe===null?t.memoizedState=e:Mc(t,Fe.memoizedState,e)},useTransition:function(){var e=hs(so)[0],t=yt().memoizedState;return[e,t]},useMutableSource:vc,useSyncExternalStore:yc,useId:jc,unstable_isNewReconciler:!1};function Nt(e,t){if(e&&e.defaultProps){t=G({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function vs(e,t,n,o){t=e.memoizedState,n=n(o,t),n=n==null?t:G({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var pl={isMounted:function(e){return(e=e._reactInternals)?Dn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var o=qe(),i=xn(e),a=$t(o,i);a.payload=t,n!=null&&(a.callback=n),t=vn(e,a,i),t!==null&&(It(t,e,i,o),ll(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var o=qe(),i=xn(e),a=$t(o,i);a.tag=1,a.payload=t,n!=null&&(a.callback=n),t=vn(e,a,i),t!==null&&(It(t,e,i,o),ll(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=qe(),o=xn(e),i=$t(n,o);i.tag=2,t!=null&&(i.callback=t),t=vn(e,i,o),t!==null&&(It(t,e,o,n),ll(t,e,o))}};function _c(e,t,n,o,i,a,p){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(o,a,p):t.prototype&&t.prototype.isPureReactComponent?!br(n,o)||!br(i,a):!0}function zc(e,t,n){var o=!1,i=hn,a=t.contextType;return typeof a=="object"&&a!==null?a=vt(a):(i=tt(t)?Tn:be.current,o=t.contextTypes,a=(o=o!=null)?rr(e,i):hn),t=new t(n,a),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=pl,e.stateNode=t,t._reactInternals=e,o&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=a),t}function Uc(e,t,n,o){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,o),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,o),t.state!==e&&pl.enqueueReplaceState(t,t.state,null)}function ys(e,t,n,o){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},os(e);var a=t.contextType;typeof a=="object"&&a!==null?i.context=vt(a):(a=tt(t)?Tn:be.current,i.context=rr(e,a)),i.state=e.memoizedState,a=t.getDerivedStateFromProps,typeof a=="function"&&(vs(e,t,a,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&pl.enqueueReplaceState(i,i.state,null),il(e,n,i,o),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function fr(e,t){try{var n="",o=t;do n+=he(o),o=o.return;while(o);var i=n}catch(a){i=`
Error generating stack: `+a.message+`
`+a.stack}return{value:e,source:t,stack:i,digest:null}}function As(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ws(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var pm=typeof WeakMap=="function"?WeakMap:Map;function Qc(e,t,n){n=$t(-1,n),n.tag=3,n.payload={element:null};var o=t.value;return n.callback=function(){wl||(wl=!0,Ls=o),ws(e,t)},n}function Vc(e,t,n){n=$t(-1,n),n.tag=3;var o=e.type.getDerivedStateFromError;if(typeof o=="function"){var i=t.value;n.payload=function(){return o(i)},n.callback=function(){ws(e,t)}}var a=e.stateNode;return a!==null&&typeof a.componentDidCatch=="function"&&(n.callback=function(){ws(e,t),typeof o!="function"&&(An===null?An=new Set([this]):An.add(this));var p=t.stack;this.componentDidCatch(t.value,{componentStack:p!==null?p:""})}),n}function Wc(e,t,n){var o=e.pingCache;if(o===null){o=e.pingCache=new pm;var i=new Set;o.set(t,i)}else i=o.get(t),i===void 0&&(i=new Set,o.set(t,i));i.has(n)||(i.add(n),e=Pm.bind(null,e,t,n),t.then(e,e))}function Hc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Yc(e,t,n,o,i){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=$t(-1,1),t.tag=2,vn(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=i,e)}var hm=F.ReactCurrentOwner,nt=!1;function Ze(e,t,n,o){t.child=e===null?fc(t,null,n,o):sr(t,e.child,n,o)}function Kc(e,t,n,o,i){n=n.render;var a=t.ref;return ur(t,i),o=fs(e,t,n,o,a,i),n=ds(),e!==null&&!nt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Zt(e,t,i)):(Ie&&n&&bi(t),t.flags|=1,Ze(e,t,o,i),t.child)}function Gc(e,t,n,o,i){if(e===null){var a=n.type;return typeof a=="function"&&!Vs(a)&&a.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=a,bc(e,t,a,o,i)):(e=Nl(n.type,null,o,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(a=e.child,(e.lanes&i)===0){var p=a.memoizedProps;if(n=n.compare,n=n!==null?n:br,n(p,o)&&e.ref===t.ref)return Zt(e,t,i)}return t.flags|=1,e=Sn(a,o),e.ref=t.ref,e.return=t,t.child=e}function bc(e,t,n,o,i){if(e!==null){var a=e.memoizedProps;if(br(a,o)&&e.ref===t.ref)if(nt=!1,t.pendingProps=o=a,(e.lanes&i)!==0)(e.flags&131072)!==0&&(nt=!0);else return t.lanes=e.lanes,Zt(e,t,i)}return xs(e,t,n,o,i)}function Jc(e,t,n){var o=t.pendingProps,i=o.children,a=e!==null?e.memoizedState:null;if(o.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ne(pr,ct),ct|=n;else{if((n&1073741824)===0)return e=a!==null?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ne(pr,ct),ct|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},o=a!==null?a.baseLanes:n,Ne(pr,ct),ct|=o}else a!==null?(o=a.baseLanes|n,t.memoizedState=null):o=n,Ne(pr,ct),ct|=o;return Ze(e,t,i,n),t.child}function Xc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function xs(e,t,n,o,i){var a=tt(n)?Tn:be.current;return a=rr(t,a),ur(t,i),n=fs(e,t,n,o,a,i),o=ds(),e!==null&&!nt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Zt(e,t,i)):(Ie&&o&&bi(t),t.flags|=1,Ze(e,t,n,i),t.child)}function $c(e,t,n,o,i){if(tt(n)){var a=!0;$o(t)}else a=!1;if(ur(t,i),t.stateNode===null)ml(e,t),zc(t,n,o),ys(t,n,o,i),o=!0;else if(e===null){var p=t.stateNode,x=t.memoizedProps;p.props=x;var k=p.context,T=n.contextType;typeof T=="object"&&T!==null?T=vt(T):(T=tt(n)?Tn:be.current,T=rr(t,T));var W=n.getDerivedStateFromProps,H=typeof W=="function"||typeof p.getSnapshotBeforeUpdate=="function";H||typeof p.UNSAFE_componentWillReceiveProps!="function"&&typeof p.componentWillReceiveProps!="function"||(x!==o||k!==T)&&Uc(t,p,o,T),gn=!1;var V=t.memoizedState;p.state=V,il(t,o,p,i),k=t.memoizedState,x!==o||V!==k||et.current||gn?(typeof W=="function"&&(vs(t,n,W,o),k=t.memoizedState),(x=gn||_c(t,n,x,o,V,k,T))?(H||typeof p.UNSAFE_componentWillMount!="function"&&typeof p.componentWillMount!="function"||(typeof p.componentWillMount=="function"&&p.componentWillMount(),typeof p.UNSAFE_componentWillMount=="function"&&p.UNSAFE_componentWillMount()),typeof p.componentDidMount=="function"&&(t.flags|=4194308)):(typeof p.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=o,t.memoizedState=k),p.props=o,p.state=k,p.context=T,o=x):(typeof p.componentDidMount=="function"&&(t.flags|=4194308),o=!1)}else{p=t.stateNode,pc(e,t),x=t.memoizedProps,T=t.type===t.elementType?x:Nt(t.type,x),p.props=T,H=t.pendingProps,V=p.context,k=n.contextType,typeof k=="object"&&k!==null?k=vt(k):(k=tt(n)?Tn:be.current,k=rr(t,k));var $=n.getDerivedStateFromProps;(W=typeof $=="function"||typeof p.getSnapshotBeforeUpdate=="function")||typeof p.UNSAFE_componentWillReceiveProps!="function"&&typeof p.componentWillReceiveProps!="function"||(x!==H||V!==k)&&Uc(t,p,o,k),gn=!1,V=t.memoizedState,p.state=V,il(t,o,p,i);var ee=t.memoizedState;x!==H||V!==ee||et.current||gn?(typeof $=="function"&&(vs(t,n,$,o),ee=t.memoizedState),(T=gn||_c(t,n,T,o,V,ee,k)||!1)?(W||typeof p.UNSAFE_componentWillUpdate!="function"&&typeof p.componentWillUpdate!="function"||(typeof p.componentWillUpdate=="function"&&p.componentWillUpdate(o,ee,k),typeof p.UNSAFE_componentWillUpdate=="function"&&p.UNSAFE_componentWillUpdate(o,ee,k)),typeof p.componentDidUpdate=="function"&&(t.flags|=4),typeof p.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof p.componentDidUpdate!="function"||x===e.memoizedProps&&V===e.memoizedState||(t.flags|=4),typeof p.getSnapshotBeforeUpdate!="function"||x===e.memoizedProps&&V===e.memoizedState||(t.flags|=1024),t.memoizedProps=o,t.memoizedState=ee),p.props=o,p.state=ee,p.context=k,o=T):(typeof p.componentDidUpdate!="function"||x===e.memoizedProps&&V===e.memoizedState||(t.flags|=4),typeof p.getSnapshotBeforeUpdate!="function"||x===e.memoizedProps&&V===e.memoizedState||(t.flags|=1024),o=!1)}return Cs(e,t,n,o,a,i)}function Cs(e,t,n,o,i,a){Xc(e,t);var p=(t.flags&128)!==0;if(!o&&!p)return i&&nc(t,n,!1),Zt(e,t,a);o=t.stateNode,hm.current=t;var x=p&&typeof n.getDerivedStateFromError!="function"?null:o.render();return t.flags|=1,e!==null&&p?(t.child=sr(t,e.child,null,a),t.child=sr(t,null,x,a)):Ze(e,t,x,a),t.memoizedState=o.state,i&&nc(t,n,!0),t.child}function Zc(e){var t=e.stateNode;t.pendingContext?ec(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ec(e,t.context,!1),ls(e,t.containerInfo)}function qc(e,t,n,o,i){return ir(),Zi(i),t.flags|=256,Ze(e,t,n,o),t.child}var Ss={dehydrated:null,treeContext:null,retryLane:0};function Es(e){return{baseLanes:e,cachePool:null,transitions:null}}function ef(e,t,n){var o=t.pendingProps,i=De.current,a=!1,p=(t.flags&128)!==0,x;if((x=p)||(x=e!==null&&e.memoizedState===null?!1:(i&2)!==0),x?(a=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),Ne(De,i&1),e===null)return $i(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(p=o.children,e=o.fallback,a?(o=t.mode,a=t.child,p={mode:"hidden",children:p},(o&1)===0&&a!==null?(a.childLanes=0,a.pendingProps=p):a=Pl(p,o,0,null),e=Vn(e,o,n,null),a.return=t,e.return=t,a.sibling=e,t.child=a,t.child.memoizedState=Es(n),t.memoizedState=Ss,e):ks(t,p));if(i=e.memoizedState,i!==null&&(x=i.dehydrated,x!==null))return mm(e,t,p,o,x,i,n);if(a){a=o.fallback,p=t.mode,i=e.child,x=i.sibling;var k={mode:"hidden",children:o.children};return(p&1)===0&&t.child!==i?(o=t.child,o.childLanes=0,o.pendingProps=k,t.deletions=null):(o=Sn(i,k),o.subtreeFlags=i.subtreeFlags&14680064),x!==null?a=Sn(x,a):(a=Vn(a,p,n,null),a.flags|=2),a.return=t,o.return=t,o.sibling=a,t.child=o,o=a,a=t.child,p=e.child.memoizedState,p=p===null?Es(n):{baseLanes:p.baseLanes|n,cachePool:null,transitions:p.transitions},a.memoizedState=p,a.childLanes=e.childLanes&~n,t.memoizedState=Ss,o}return a=e.child,e=a.sibling,o=Sn(a,{mode:"visible",children:o.children}),(t.mode&1)===0&&(o.lanes=n),o.return=t,o.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function ks(e,t){return t=Pl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function hl(e,t,n,o){return o!==null&&Zi(o),sr(t,e.child,null,n),e=ks(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function mm(e,t,n,o,i,a,p){if(n)return t.flags&256?(t.flags&=-257,o=As(Error(s(422))),hl(e,t,p,o)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(a=o.fallback,i=t.mode,o=Pl({mode:"visible",children:o.children},i,0,null),a=Vn(a,i,p,null),a.flags|=2,o.return=t,a.return=t,o.sibling=a,t.child=o,(t.mode&1)!==0&&sr(t,e.child,null,p),t.child.memoizedState=Es(p),t.memoizedState=Ss,a);if((t.mode&1)===0)return hl(e,t,p,null);if(i.data==="$!"){if(o=i.nextSibling&&i.nextSibling.dataset,o)var x=o.dgst;return o=x,a=Error(s(419)),o=As(a,o,void 0),hl(e,t,p,o)}if(x=(p&e.childLanes)!==0,nt||x){if(o=Ue,o!==null){switch(p&-p){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=(i&(o.suspendedLanes|p))!==0?0:i,i!==0&&i!==a.retryLane&&(a.retryLane=i,Xt(e,i),It(o,e,i,-1))}return Qs(),o=As(Error(s(421))),hl(e,t,p,o)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Rm.bind(null,e),i._reactRetry=t,null):(e=a.treeContext,ut=dn(i.nextSibling),at=t,Ie=!0,kt=null,e!==null&&(mt[gt++]=bt,mt[gt++]=Jt,mt[gt++]=Mn,bt=e.id,Jt=e.overflow,Mn=t),t=ks(t,o.children),t.flags|=4096,t)}function tf(e,t,n){e.lanes|=t;var o=e.alternate;o!==null&&(o.lanes|=t),ns(e.return,t,n)}function Ns(e,t,n,o,i){var a=e.memoizedState;a===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:o,tail:n,tailMode:i}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=o,a.tail=n,a.tailMode=i)}function nf(e,t,n){var o=t.pendingProps,i=o.revealOrder,a=o.tail;if(Ze(e,t,o.children,n),o=De.current,(o&2)!==0)o=o&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&tf(e,n,t);else if(e.tag===19)tf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}if(Ne(De,o),(t.mode&1)===0)t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&sl(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ns(t,!1,i,n,a);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&sl(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ns(t,!0,n,null,a);break;case"together":Ns(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ml(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Zt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),_n|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,n=Sn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Sn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function gm(e,t,n){switch(t.tag){case 3:Zc(t),ir();break;case 5:gc(t);break;case 1:tt(t.type)&&$o(t);break;case 4:ls(t,t.stateNode.containerInfo);break;case 10:var o=t.type._context,i=t.memoizedProps.value;Ne(rl,o._currentValue),o._currentValue=i;break;case 13:if(o=t.memoizedState,o!==null)return o.dehydrated!==null?(Ne(De,De.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?ef(e,t,n):(Ne(De,De.current&1),e=Zt(e,t,n),e!==null?e.sibling:null);Ne(De,De.current&1);break;case 19:if(o=(n&t.childLanes)!==0,(e.flags&128)!==0){if(o)return nf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),Ne(De,De.current),o)break;return null;case 22:case 23:return t.lanes=0,Jc(e,t,n)}return Zt(e,t,n)}var rf,Ps,of,lf;rf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ps=function(){},of=function(e,t,n,o){var i=e.memoizedProps;if(i!==o){e=t.stateNode,Bn(Bt.current);var a=null;switch(n){case"input":i=Me(e,i),o=Me(e,o),a=[];break;case"select":i=G({},i,{value:void 0}),o=G({},o,{value:void 0}),a=[];break;case"textarea":i=ht(e,i),o=ht(e,o),a=[];break;default:typeof i.onClick!="function"&&typeof o.onClick=="function"&&(e.onclick=bo)}si(n,o);var p;n=null;for(T in i)if(!o.hasOwnProperty(T)&&i.hasOwnProperty(T)&&i[T]!=null)if(T==="style"){var x=i[T];for(p in x)x.hasOwnProperty(p)&&(n||(n={}),n[p]="")}else T!=="dangerouslySetInnerHTML"&&T!=="children"&&T!=="suppressContentEditableWarning"&&T!=="suppressHydrationWarning"&&T!=="autoFocus"&&(c.hasOwnProperty(T)?a||(a=[]):(a=a||[]).push(T,null));for(T in o){var k=o[T];if(x=i!=null?i[T]:void 0,o.hasOwnProperty(T)&&k!==x&&(k!=null||x!=null))if(T==="style")if(x){for(p in x)!x.hasOwnProperty(p)||k&&k.hasOwnProperty(p)||(n||(n={}),n[p]="");for(p in k)k.hasOwnProperty(p)&&x[p]!==k[p]&&(n||(n={}),n[p]=k[p])}else n||(a||(a=[]),a.push(T,n)),n=k;else T==="dangerouslySetInnerHTML"?(k=k?k.__html:void 0,x=x?x.__html:void 0,k!=null&&x!==k&&(a=a||[]).push(T,k)):T==="children"?typeof k!="string"&&typeof k!="number"||(a=a||[]).push(T,""+k):T!=="suppressContentEditableWarning"&&T!=="suppressHydrationWarning"&&(c.hasOwnProperty(T)?(k!=null&&T==="onScroll"&&Pe("scroll",e),a||x===k||(a=[])):(a=a||[]).push(T,k))}n&&(a=a||[]).push("style",n);var T=a;(t.updateQueue=T)&&(t.flags|=4)}},lf=function(e,t,n,o){n!==o&&(t.flags|=4)};function uo(e,t){if(!Ie)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var o=null;n!==null;)n.alternate!==null&&(o=n),n=n.sibling;o===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:o.sibling=null}}function Xe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,o=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,o|=i.subtreeFlags&14680064,o|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,o|=i.subtreeFlags,o|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=o,e.childLanes=n,t}function vm(e,t,n){var o=t.pendingProps;switch(Ji(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Xe(t),null;case 1:return tt(t.type)&&Xo(),Xe(t),null;case 3:return o=t.stateNode,cr(),Re(et),Re(be),as(),o.pendingContext&&(o.context=o.pendingContext,o.pendingContext=null),(e===null||e.child===null)&&(tl(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,kt!==null&&(_s(kt),kt=null))),Ps(e,t),Xe(t),null;case 5:is(t);var i=Bn(oo.current);if(n=t.type,e!==null&&t.stateNode!=null)of(e,t,n,o,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!o){if(t.stateNode===null)throw Error(s(166));return Xe(t),null}if(e=Bn(Bt.current),tl(t)){o=t.stateNode,n=t.type;var a=t.memoizedProps;switch(o[Lt]=t,o[qr]=a,e=(t.mode&1)!==0,n){case"dialog":Pe("cancel",o),Pe("close",o);break;case"iframe":case"object":case"embed":Pe("load",o);break;case"video":case"audio":for(i=0;i<Xr.length;i++)Pe(Xr[i],o);break;case"source":Pe("error",o);break;case"img":case"image":case"link":Pe("error",o),Pe("load",o);break;case"details":Pe("toggle",o);break;case"input":Ge(o,a),Pe("invalid",o);break;case"select":o._wrapperState={wasMultiple:!!a.multiple},Pe("invalid",o);break;case"textarea":Va(o,a),Pe("invalid",o)}si(n,a),i=null;for(var p in a)if(a.hasOwnProperty(p)){var x=a[p];p==="children"?typeof x=="string"?o.textContent!==x&&(a.suppressHydrationWarning!==!0&&Go(o.textContent,x,e),i=["children",x]):typeof x=="number"&&o.textContent!==""+x&&(a.suppressHydrationWarning!==!0&&Go(o.textContent,x,e),i=["children",""+x]):c.hasOwnProperty(p)&&x!=null&&p==="onScroll"&&Pe("scroll",o)}switch(n){case"input":Ke(o),Yn(o,a,!0);break;case"textarea":Ke(o),Ha(o);break;case"select":case"option":break;default:typeof a.onClick=="function"&&(o.onclick=bo)}o=i,t.updateQueue=o,o!==null&&(t.flags|=4)}else{p=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ya(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=p.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof o.is=="string"?e=p.createElement(n,{is:o.is}):(e=p.createElement(n),n==="select"&&(p=e,o.multiple?p.multiple=!0:o.size&&(p.size=o.size))):e=p.createElementNS(e,n),e[Lt]=t,e[qr]=o,rf(e,t,!1,!1),t.stateNode=e;e:{switch(p=ai(n,o),n){case"dialog":Pe("cancel",e),Pe("close",e),i=o;break;case"iframe":case"object":case"embed":Pe("load",e),i=o;break;case"video":case"audio":for(i=0;i<Xr.length;i++)Pe(Xr[i],e);i=o;break;case"source":Pe("error",e),i=o;break;case"img":case"image":case"link":Pe("error",e),Pe("load",e),i=o;break;case"details":Pe("toggle",e),i=o;break;case"input":Ge(e,o),i=Me(e,o),Pe("invalid",e);break;case"option":i=o;break;case"select":e._wrapperState={wasMultiple:!!o.multiple},i=G({},o,{value:void 0}),Pe("invalid",e);break;case"textarea":Va(e,o),i=ht(e,o),Pe("invalid",e);break;default:i=o}si(n,i),x=i;for(a in x)if(x.hasOwnProperty(a)){var k=x[a];a==="style"?ba(e,k):a==="dangerouslySetInnerHTML"?(k=k?k.__html:void 0,k!=null&&Ka(e,k)):a==="children"?typeof k=="string"?(n!=="textarea"||k!=="")&&Or(e,k):typeof k=="number"&&Or(e,""+k):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(c.hasOwnProperty(a)?k!=null&&a==="onScroll"&&Pe("scroll",e):k!=null&&L(e,a,k,p))}switch(n){case"input":Ke(e),Yn(e,o,!1);break;case"textarea":Ke(e),Ha(e);break;case"option":o.value!=null&&e.setAttribute("value",""+de(o.value));break;case"select":e.multiple=!!o.multiple,a=o.value,a!=null?Ct(e,!!o.multiple,a,!1):o.defaultValue!=null&&Ct(e,!!o.multiple,o.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=bo)}switch(n){case"button":case"input":case"select":case"textarea":o=!!o.autoFocus;break e;case"img":o=!0;break e;default:o=!1}}o&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Xe(t),null;case 6:if(e&&t.stateNode!=null)lf(e,t,e.memoizedProps,o);else{if(typeof o!="string"&&t.stateNode===null)throw Error(s(166));if(n=Bn(oo.current),Bn(Bt.current),tl(t)){if(o=t.stateNode,n=t.memoizedProps,o[Lt]=t,(a=o.nodeValue!==n)&&(e=at,e!==null))switch(e.tag){case 3:Go(o.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Go(o.nodeValue,n,(e.mode&1)!==0)}a&&(t.flags|=4)}else o=(n.nodeType===9?n:n.ownerDocument).createTextNode(o),o[Lt]=t,t.stateNode=o}return Xe(t),null;case 13:if(Re(De),o=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ie&&ut!==null&&(t.mode&1)!==0&&(t.flags&128)===0)ac(),ir(),t.flags|=98560,a=!1;else if(a=tl(t),o!==null&&o.dehydrated!==null){if(e===null){if(!a)throw Error(s(318));if(a=t.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(s(317));a[Lt]=t}else ir(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Xe(t),a=!1}else kt!==null&&(_s(kt),kt=null),a=!0;if(!a)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(o=o!==null,o!==(e!==null&&e.memoizedState!==null)&&o&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(De.current&1)!==0?_e===0&&(_e=3):Qs())),t.updateQueue!==null&&(t.flags|=4),Xe(t),null);case 4:return cr(),Ps(e,t),e===null&&$r(t.stateNode.containerInfo),Xe(t),null;case 10:return ts(t.type._context),Xe(t),null;case 17:return tt(t.type)&&Xo(),Xe(t),null;case 19:if(Re(De),a=t.memoizedState,a===null)return Xe(t),null;if(o=(t.flags&128)!==0,p=a.rendering,p===null)if(o)uo(a,!1);else{if(_e!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(p=sl(e),p!==null){for(t.flags|=128,uo(a,!1),o=p.updateQueue,o!==null&&(t.updateQueue=o,t.flags|=4),t.subtreeFlags=0,o=n,n=t.child;n!==null;)a=n,e=o,a.flags&=14680066,p=a.alternate,p===null?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=p.childLanes,a.lanes=p.lanes,a.child=p.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=p.memoizedProps,a.memoizedState=p.memoizedState,a.updateQueue=p.updateQueue,a.type=p.type,e=p.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ne(De,De.current&1|2),t.child}e=e.sibling}a.tail!==null&&je()>hr&&(t.flags|=128,o=!0,uo(a,!1),t.lanes=4194304)}else{if(!o)if(e=sl(p),e!==null){if(t.flags|=128,o=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),uo(a,!0),a.tail===null&&a.tailMode==="hidden"&&!p.alternate&&!Ie)return Xe(t),null}else 2*je()-a.renderingStartTime>hr&&n!==1073741824&&(t.flags|=128,o=!0,uo(a,!1),t.lanes=4194304);a.isBackwards?(p.sibling=t.child,t.child=p):(n=a.last,n!==null?n.sibling=p:t.child=p,a.last=p)}return a.tail!==null?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=je(),t.sibling=null,n=De.current,Ne(De,o?n&1|2:n&1),t):(Xe(t),null);case 22:case 23:return Us(),o=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==o&&(t.flags|=8192),o&&(t.mode&1)!==0?(ct&1073741824)!==0&&(Xe(t),t.subtreeFlags&6&&(t.flags|=8192)):Xe(t),null;case 24:return null;case 25:return null}throw Error(s(156,t.tag))}function ym(e,t){switch(Ji(t),t.tag){case 1:return tt(t.type)&&Xo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return cr(),Re(et),Re(be),as(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return is(t),null;case 13:if(Re(De),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));ir()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Re(De),null;case 4:return cr(),null;case 10:return ts(t.type._context),null;case 22:case 23:return Us(),null;case 24:return null;default:return null}}var gl=!1,$e=!1,Am=typeof WeakSet=="function"?WeakSet:Set,q=null;function dr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(o){Te(e,t,o)}else n.current=null}function Rs(e,t,n){try{n()}catch(o){Te(e,t,o)}}var sf=!1;function wm(e,t){if(Ui=Bo,e=_u(),Ti(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var o=n.getSelection&&n.getSelection();if(o&&o.rangeCount!==0){n=o.anchorNode;var i=o.anchorOffset,a=o.focusNode;o=o.focusOffset;try{n.nodeType,a.nodeType}catch{n=null;break e}var p=0,x=-1,k=-1,T=0,W=0,H=e,V=null;t:for(;;){for(var $;H!==n||i!==0&&H.nodeType!==3||(x=p+i),H!==a||o!==0&&H.nodeType!==3||(k=p+o),H.nodeType===3&&(p+=H.nodeValue.length),($=H.firstChild)!==null;)V=H,H=$;for(;;){if(H===e)break t;if(V===n&&++T===i&&(x=p),V===a&&++W===o&&(k=p),($=H.nextSibling)!==null)break;H=V,V=H.parentNode}H=$}n=x===-1||k===-1?null:{start:x,end:k}}else n=null}n=n||{start:0,end:0}}else n=null;for(Qi={focusedElem:e,selectionRange:n},Bo=!1,q=t;q!==null;)if(t=q,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,q=e;else for(;q!==null;){t=q;try{var ee=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(ee!==null){var ne=ee.memoizedProps,Le=ee.memoizedState,D=t.stateNode,N=D.getSnapshotBeforeUpdate(t.elementType===t.type?ne:Nt(t.type,ne),Le);D.__reactInternalSnapshotBeforeUpdate=N}break;case 3:var O=t.stateNode.containerInfo;O.nodeType===1?O.textContent="":O.nodeType===9&&O.documentElement&&O.removeChild(O.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(s(163))}}catch(K){Te(t,t.return,K)}if(e=t.sibling,e!==null){e.return=t.return,q=e;break}q=t.return}return ee=sf,sf=!1,ee}function co(e,t,n){var o=t.updateQueue;if(o=o!==null?o.lastEffect:null,o!==null){var i=o=o.next;do{if((i.tag&e)===e){var a=i.destroy;i.destroy=void 0,a!==void 0&&Rs(t,n,a)}i=i.next}while(i!==o)}}function vl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var o=n.create;n.destroy=o()}n=n.next}while(n!==t)}}function Is(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function af(e){var t=e.alternate;t!==null&&(e.alternate=null,af(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Lt],delete t[qr],delete t[Yi],delete t[nm],delete t[rm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function uf(e){return e.tag===5||e.tag===3||e.tag===4}function cf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||uf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ds(e,t,n){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=bo));else if(o!==4&&(e=e.child,e!==null))for(Ds(e,t,n),e=e.sibling;e!==null;)Ds(e,t,n),e=e.sibling}function Os(e,t,n){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(o!==4&&(e=e.child,e!==null))for(Os(e,t,n),e=e.sibling;e!==null;)Os(e,t,n),e=e.sibling}var Ve=null,Pt=!1;function yn(e,t,n){for(n=n.child;n!==null;)ff(e,t,n),n=n.sibling}function ff(e,t,n){if(jt&&typeof jt.onCommitFiberUnmount=="function")try{jt.onCommitFiberUnmount(Do,n)}catch{}switch(n.tag){case 5:$e||dr(n,t);case 6:var o=Ve,i=Pt;Ve=null,yn(e,t,n),Ve=o,Pt=i,Ve!==null&&(Pt?(e=Ve,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ve.removeChild(n.stateNode));break;case 18:Ve!==null&&(Pt?(e=Ve,n=n.stateNode,e.nodeType===8?Hi(e.parentNode,n):e.nodeType===1&&Hi(e,n),Vr(e)):Hi(Ve,n.stateNode));break;case 4:o=Ve,i=Pt,Ve=n.stateNode.containerInfo,Pt=!0,yn(e,t,n),Ve=o,Pt=i;break;case 0:case 11:case 14:case 15:if(!$e&&(o=n.updateQueue,o!==null&&(o=o.lastEffect,o!==null))){i=o=o.next;do{var a=i,p=a.destroy;a=a.tag,p!==void 0&&((a&2)!==0||(a&4)!==0)&&Rs(n,t,p),i=i.next}while(i!==o)}yn(e,t,n);break;case 1:if(!$e&&(dr(n,t),o=n.stateNode,typeof o.componentWillUnmount=="function"))try{o.props=n.memoizedProps,o.state=n.memoizedState,o.componentWillUnmount()}catch(x){Te(n,t,x)}yn(e,t,n);break;case 21:yn(e,t,n);break;case 22:n.mode&1?($e=(o=$e)||n.memoizedState!==null,yn(e,t,n),$e=o):yn(e,t,n);break;default:yn(e,t,n)}}function df(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Am),t.forEach(function(o){var i=Im.bind(null,e,o);n.has(o)||(n.add(o),o.then(i,i))})}}function Rt(e,t){var n=t.deletions;if(n!==null)for(var o=0;o<n.length;o++){var i=n[o];try{var a=e,p=t,x=p;e:for(;x!==null;){switch(x.tag){case 5:Ve=x.stateNode,Pt=!1;break e;case 3:Ve=x.stateNode.containerInfo,Pt=!0;break e;case 4:Ve=x.stateNode.containerInfo,Pt=!0;break e}x=x.return}if(Ve===null)throw Error(s(160));ff(a,p,i),Ve=null,Pt=!1;var k=i.alternate;k!==null&&(k.return=null),i.return=null}catch(T){Te(i,t,T)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)pf(t,e),t=t.sibling}function pf(e,t){var n=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Rt(t,e),_t(e),o&4){try{co(3,e,e.return),vl(3,e)}catch(ne){Te(e,e.return,ne)}try{co(5,e,e.return)}catch(ne){Te(e,e.return,ne)}}break;case 1:Rt(t,e),_t(e),o&512&&n!==null&&dr(n,n.return);break;case 5:if(Rt(t,e),_t(e),o&512&&n!==null&&dr(n,n.return),e.flags&32){var i=e.stateNode;try{Or(i,"")}catch(ne){Te(e,e.return,ne)}}if(o&4&&(i=e.stateNode,i!=null)){var a=e.memoizedProps,p=n!==null?n.memoizedProps:a,x=e.type,k=e.updateQueue;if(e.updateQueue=null,k!==null)try{x==="input"&&a.type==="radio"&&a.name!=null&&Mt(i,a),ai(x,p);var T=ai(x,a);for(p=0;p<k.length;p+=2){var W=k[p],H=k[p+1];W==="style"?ba(i,H):W==="dangerouslySetInnerHTML"?Ka(i,H):W==="children"?Or(i,H):L(i,W,H,T)}switch(x){case"input":rn(i,a);break;case"textarea":Wa(i,a);break;case"select":var V=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!a.multiple;var $=a.value;$!=null?Ct(i,!!a.multiple,$,!1):V!==!!a.multiple&&(a.defaultValue!=null?Ct(i,!!a.multiple,a.defaultValue,!0):Ct(i,!!a.multiple,a.multiple?[]:"",!1))}i[qr]=a}catch(ne){Te(e,e.return,ne)}}break;case 6:if(Rt(t,e),_t(e),o&4){if(e.stateNode===null)throw Error(s(162));i=e.stateNode,a=e.memoizedProps;try{i.nodeValue=a}catch(ne){Te(e,e.return,ne)}}break;case 3:if(Rt(t,e),_t(e),o&4&&n!==null&&n.memoizedState.isDehydrated)try{Vr(t.containerInfo)}catch(ne){Te(e,e.return,ne)}break;case 4:Rt(t,e),_t(e);break;case 13:Rt(t,e),_t(e),i=e.child,i.flags&8192&&(a=i.memoizedState!==null,i.stateNode.isHidden=a,!a||i.alternate!==null&&i.alternate.memoizedState!==null||(js=je())),o&4&&df(e);break;case 22:if(W=n!==null&&n.memoizedState!==null,e.mode&1?($e=(T=$e)||W,Rt(t,e),$e=T):Rt(t,e),_t(e),o&8192){if(T=e.memoizedState!==null,(e.stateNode.isHidden=T)&&!W&&(e.mode&1)!==0)for(q=e,W=e.child;W!==null;){for(H=q=W;q!==null;){switch(V=q,$=V.child,V.tag){case 0:case 11:case 14:case 15:co(4,V,V.return);break;case 1:dr(V,V.return);var ee=V.stateNode;if(typeof ee.componentWillUnmount=="function"){o=V,n=V.return;try{t=o,ee.props=t.memoizedProps,ee.state=t.memoizedState,ee.componentWillUnmount()}catch(ne){Te(o,n,ne)}}break;case 5:dr(V,V.return);break;case 22:if(V.memoizedState!==null){gf(H);continue}}$!==null?($.return=V,q=$):gf(H)}W=W.sibling}e:for(W=null,H=e;;){if(H.tag===5){if(W===null){W=H;try{i=H.stateNode,T?(a=i.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none"):(x=H.stateNode,k=H.memoizedProps.style,p=k!=null&&k.hasOwnProperty("display")?k.display:null,x.style.display=Ga("display",p))}catch(ne){Te(e,e.return,ne)}}}else if(H.tag===6){if(W===null)try{H.stateNode.nodeValue=T?"":H.memoizedProps}catch(ne){Te(e,e.return,ne)}}else if((H.tag!==22&&H.tag!==23||H.memoizedState===null||H===e)&&H.child!==null){H.child.return=H,H=H.child;continue}if(H===e)break e;for(;H.sibling===null;){if(H.return===null||H.return===e)break e;W===H&&(W=null),H=H.return}W===H&&(W=null),H.sibling.return=H.return,H=H.sibling}}break;case 19:Rt(t,e),_t(e),o&4&&df(e);break;case 21:break;default:Rt(t,e),_t(e)}}function _t(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(uf(n)){var o=n;break e}n=n.return}throw Error(s(160))}switch(o.tag){case 5:var i=o.stateNode;o.flags&32&&(Or(i,""),o.flags&=-33);var a=cf(e);Os(e,a,i);break;case 3:case 4:var p=o.stateNode.containerInfo,x=cf(e);Ds(e,x,p);break;default:throw Error(s(161))}}catch(k){Te(e,e.return,k)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function xm(e,t,n){q=e,hf(e)}function hf(e,t,n){for(var o=(e.mode&1)!==0;q!==null;){var i=q,a=i.child;if(i.tag===22&&o){var p=i.memoizedState!==null||gl;if(!p){var x=i.alternate,k=x!==null&&x.memoizedState!==null||$e;x=gl;var T=$e;if(gl=p,($e=k)&&!T)for(q=i;q!==null;)p=q,k=p.child,p.tag===22&&p.memoizedState!==null?vf(i):k!==null?(k.return=p,q=k):vf(i);for(;a!==null;)q=a,hf(a),a=a.sibling;q=i,gl=x,$e=T}mf(e)}else(i.subtreeFlags&8772)!==0&&a!==null?(a.return=i,q=a):mf(e)}}function mf(e){for(;q!==null;){var t=q;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:$e||vl(5,t);break;case 1:var o=t.stateNode;if(t.flags&4&&!$e)if(n===null)o.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Nt(t.type,n.memoizedProps);o.componentDidUpdate(i,n.memoizedState,o.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;a!==null&&mc(t,a,o);break;case 3:var p=t.updateQueue;if(p!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}mc(t,p,n)}break;case 5:var x=t.stateNode;if(n===null&&t.flags&4){n=x;var k=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":k.autoFocus&&n.focus();break;case"img":k.src&&(n.src=k.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var T=t.alternate;if(T!==null){var W=T.memoizedState;if(W!==null){var H=W.dehydrated;H!==null&&Vr(H)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(s(163))}$e||t.flags&512&&Is(t)}catch(V){Te(t,t.return,V)}}if(t===e){q=null;break}if(n=t.sibling,n!==null){n.return=t.return,q=n;break}q=t.return}}function gf(e){for(;q!==null;){var t=q;if(t===e){q=null;break}var n=t.sibling;if(n!==null){n.return=t.return,q=n;break}q=t.return}}function vf(e){for(;q!==null;){var t=q;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{vl(4,t)}catch(k){Te(t,n,k)}break;case 1:var o=t.stateNode;if(typeof o.componentDidMount=="function"){var i=t.return;try{o.componentDidMount()}catch(k){Te(t,i,k)}}var a=t.return;try{Is(t)}catch(k){Te(t,a,k)}break;case 5:var p=t.return;try{Is(t)}catch(k){Te(t,p,k)}}}catch(k){Te(t,t.return,k)}if(t===e){q=null;break}var x=t.sibling;if(x!==null){x.return=t.return,q=x;break}q=t.return}}var Cm=Math.ceil,yl=F.ReactCurrentDispatcher,Ts=F.ReactCurrentOwner,At=F.ReactCurrentBatchConfig,Ce=0,Ue=null,Be=null,We=0,ct=0,pr=pn(0),_e=0,fo=null,_n=0,Al=0,Ms=0,po=null,rt=null,js=0,hr=1/0,qt=null,wl=!1,Ls=null,An=null,xl=!1,wn=null,Cl=0,ho=0,Bs=null,Sl=-1,El=0;function qe(){return(Ce&6)!==0?je():Sl!==-1?Sl:Sl=je()}function xn(e){return(e.mode&1)===0?1:(Ce&2)!==0&&We!==0?We&-We:lm.transition!==null?(El===0&&(El=uu()),El):(e=ke,e!==0||(e=window.event,e=e===void 0?16:yu(e.type)),e)}function It(e,t,n,o){if(50<ho)throw ho=0,Bs=null,Error(s(185));Fr(e,n,o),((Ce&2)===0||e!==Ue)&&(e===Ue&&((Ce&2)===0&&(Al|=n),_e===4&&Cn(e,We)),ot(e,o),n===1&&Ce===0&&(t.mode&1)===0&&(hr=je()+500,Zo&&mn()))}function ot(e,t){var n=e.callbackNode;lh(e,t);var o=Mo(e,e===Ue?We:0);if(o===0)n!==null&&iu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=o&-o,e.callbackPriority!==t){if(n!=null&&iu(n),t===1)e.tag===0?om(Af.bind(null,e)):rc(Af.bind(null,e)),em(function(){(Ce&6)===0&&mn()}),n=null;else{switch(cu(o)){case 1:n=mi;break;case 4:n=su;break;case 16:n=Io;break;case 536870912:n=au;break;default:n=Io}n=Pf(n,yf.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function yf(e,t){if(Sl=-1,El=0,(Ce&6)!==0)throw Error(s(327));var n=e.callbackNode;if(mr()&&e.callbackNode!==n)return null;var o=Mo(e,e===Ue?We:0);if(o===0)return null;if((o&30)!==0||(o&e.expiredLanes)!==0||t)t=kl(e,o);else{t=o;var i=Ce;Ce|=2;var a=xf();(Ue!==e||We!==t)&&(qt=null,hr=je()+500,Un(e,t));do try{km();break}catch(x){wf(e,x)}while(!0);es(),yl.current=a,Ce=i,Be!==null?t=0:(Ue=null,We=0,t=_e)}if(t!==0){if(t===2&&(i=gi(e),i!==0&&(o=i,t=Fs(e,i))),t===1)throw n=fo,Un(e,0),Cn(e,o),ot(e,je()),n;if(t===6)Cn(e,o);else{if(i=e.current.alternate,(o&30)===0&&!Sm(i)&&(t=kl(e,o),t===2&&(a=gi(e),a!==0&&(o=a,t=Fs(e,a))),t===1))throw n=fo,Un(e,0),Cn(e,o),ot(e,je()),n;switch(e.finishedWork=i,e.finishedLanes=o,t){case 0:case 1:throw Error(s(345));case 2:Qn(e,rt,qt);break;case 3:if(Cn(e,o),(o&130023424)===o&&(t=js+500-je(),10<t)){if(Mo(e,0)!==0)break;if(i=e.suspendedLanes,(i&o)!==o){qe(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Wi(Qn.bind(null,e,rt,qt),t);break}Qn(e,rt,qt);break;case 4:if(Cn(e,o),(o&4194240)===o)break;for(t=e.eventTimes,i=-1;0<o;){var p=31-St(o);a=1<<p,p=t[p],p>i&&(i=p),o&=~a}if(o=i,o=je()-o,o=(120>o?120:480>o?480:1080>o?1080:1920>o?1920:3e3>o?3e3:4320>o?4320:1960*Cm(o/1960))-o,10<o){e.timeoutHandle=Wi(Qn.bind(null,e,rt,qt),o);break}Qn(e,rt,qt);break;case 5:Qn(e,rt,qt);break;default:throw Error(s(329))}}}return ot(e,je()),e.callbackNode===n?yf.bind(null,e):null}function Fs(e,t){var n=po;return e.current.memoizedState.isDehydrated&&(Un(e,t).flags|=256),e=kl(e,t),e!==2&&(t=rt,rt=n,t!==null&&_s(t)),e}function _s(e){rt===null?rt=e:rt.push.apply(rt,e)}function Sm(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var o=0;o<n.length;o++){var i=n[o],a=i.getSnapshot;i=i.value;try{if(!Et(a(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Cn(e,t){for(t&=~Ms,t&=~Al,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-St(t),o=1<<n;e[n]=-1,t&=~o}}function Af(e){if((Ce&6)!==0)throw Error(s(327));mr();var t=Mo(e,0);if((t&1)===0)return ot(e,je()),null;var n=kl(e,t);if(e.tag!==0&&n===2){var o=gi(e);o!==0&&(t=o,n=Fs(e,o))}if(n===1)throw n=fo,Un(e,0),Cn(e,t),ot(e,je()),n;if(n===6)throw Error(s(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Qn(e,rt,qt),ot(e,je()),null}function zs(e,t){var n=Ce;Ce|=1;try{return e(t)}finally{Ce=n,Ce===0&&(hr=je()+500,Zo&&mn())}}function zn(e){wn!==null&&wn.tag===0&&(Ce&6)===0&&mr();var t=Ce;Ce|=1;var n=At.transition,o=ke;try{if(At.transition=null,ke=1,e)return e()}finally{ke=o,At.transition=n,Ce=t,(Ce&6)===0&&mn()}}function Us(){ct=pr.current,Re(pr)}function Un(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,qh(n)),Be!==null)for(n=Be.return;n!==null;){var o=n;switch(Ji(o),o.tag){case 1:o=o.type.childContextTypes,o!=null&&Xo();break;case 3:cr(),Re(et),Re(be),as();break;case 5:is(o);break;case 4:cr();break;case 13:Re(De);break;case 19:Re(De);break;case 10:ts(o.type._context);break;case 22:case 23:Us()}n=n.return}if(Ue=e,Be=e=Sn(e.current,null),We=ct=t,_e=0,fo=null,Ms=Al=_n=0,rt=po=null,Ln!==null){for(t=0;t<Ln.length;t++)if(n=Ln[t],o=n.interleaved,o!==null){n.interleaved=null;var i=o.next,a=n.pending;if(a!==null){var p=a.next;a.next=i,o.next=p}n.pending=o}Ln=null}return e}function wf(e,t){do{var n=Be;try{if(es(),al.current=dl,ul){for(var o=Oe.memoizedState;o!==null;){var i=o.queue;i!==null&&(i.pending=null),o=o.next}ul=!1}if(Fn=0,ze=Fe=Oe=null,lo=!1,io=0,Ts.current=null,n===null||n.return===null){_e=1,fo=t,Be=null;break}e:{var a=e,p=n.return,x=n,k=t;if(t=We,x.flags|=32768,k!==null&&typeof k=="object"&&typeof k.then=="function"){var T=k,W=x,H=W.tag;if((W.mode&1)===0&&(H===0||H===11||H===15)){var V=W.alternate;V?(W.updateQueue=V.updateQueue,W.memoizedState=V.memoizedState,W.lanes=V.lanes):(W.updateQueue=null,W.memoizedState=null)}var $=Hc(p);if($!==null){$.flags&=-257,Yc($,p,x,a,t),$.mode&1&&Wc(a,T,t),t=$,k=T;var ee=t.updateQueue;if(ee===null){var ne=new Set;ne.add(k),t.updateQueue=ne}else ee.add(k);break e}else{if((t&1)===0){Wc(a,T,t),Qs();break e}k=Error(s(426))}}else if(Ie&&x.mode&1){var Le=Hc(p);if(Le!==null){(Le.flags&65536)===0&&(Le.flags|=256),Yc(Le,p,x,a,t),Zi(fr(k,x));break e}}a=k=fr(k,x),_e!==4&&(_e=2),po===null?po=[a]:po.push(a),a=p;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t;var D=Qc(a,k,t);hc(a,D);break e;case 1:x=k;var N=a.type,O=a.stateNode;if((a.flags&128)===0&&(typeof N.getDerivedStateFromError=="function"||O!==null&&typeof O.componentDidCatch=="function"&&(An===null||!An.has(O)))){a.flags|=65536,t&=-t,a.lanes|=t;var K=Vc(a,x,t);hc(a,K);break e}}a=a.return}while(a!==null)}Sf(n)}catch(re){t=re,Be===n&&n!==null&&(Be=n=n.return);continue}break}while(!0)}function xf(){var e=yl.current;return yl.current=dl,e===null?dl:e}function Qs(){(_e===0||_e===3||_e===2)&&(_e=4),Ue===null||(_n&268435455)===0&&(Al&268435455)===0||Cn(Ue,We)}function kl(e,t){var n=Ce;Ce|=2;var o=xf();(Ue!==e||We!==t)&&(qt=null,Un(e,t));do try{Em();break}catch(i){wf(e,i)}while(!0);if(es(),Ce=n,yl.current=o,Be!==null)throw Error(s(261));return Ue=null,We=0,_e}function Em(){for(;Be!==null;)Cf(Be)}function km(){for(;Be!==null&&!Xp();)Cf(Be)}function Cf(e){var t=Nf(e.alternate,e,ct);e.memoizedProps=e.pendingProps,t===null?Sf(e):Be=t,Ts.current=null}function Sf(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=vm(n,t,ct),n!==null){Be=n;return}}else{if(n=ym(n,t),n!==null){n.flags&=32767,Be=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{_e=6,Be=null;return}}if(t=t.sibling,t!==null){Be=t;return}Be=t=e}while(t!==null);_e===0&&(_e=5)}function Qn(e,t,n){var o=ke,i=At.transition;try{At.transition=null,ke=1,Nm(e,t,n,o)}finally{At.transition=i,ke=o}return null}function Nm(e,t,n,o){do mr();while(wn!==null);if((Ce&6)!==0)throw Error(s(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(s(177));e.callbackNode=null,e.callbackPriority=0;var a=n.lanes|n.childLanes;if(ih(e,a),e===Ue&&(Be=Ue=null,We=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||xl||(xl=!0,Pf(Io,function(){return mr(),null})),a=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||a){a=At.transition,At.transition=null;var p=ke;ke=1;var x=Ce;Ce|=4,Ts.current=null,wm(e,n),pf(n,e),Kh(Qi),Bo=!!Ui,Qi=Ui=null,e.current=n,xm(n),$p(),Ce=x,ke=p,At.transition=a}else e.current=n;if(xl&&(xl=!1,wn=e,Cl=i),a=e.pendingLanes,a===0&&(An=null),eh(n.stateNode),ot(e,je()),t!==null)for(o=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],o(i.value,{componentStack:i.stack,digest:i.digest});if(wl)throw wl=!1,e=Ls,Ls=null,e;return(Cl&1)!==0&&e.tag!==0&&mr(),a=e.pendingLanes,(a&1)!==0?e===Bs?ho++:(ho=0,Bs=e):ho=0,mn(),null}function mr(){if(wn!==null){var e=cu(Cl),t=At.transition,n=ke;try{if(At.transition=null,ke=16>e?16:e,wn===null)var o=!1;else{if(e=wn,wn=null,Cl=0,(Ce&6)!==0)throw Error(s(331));var i=Ce;for(Ce|=4,q=e.current;q!==null;){var a=q,p=a.child;if((q.flags&16)!==0){var x=a.deletions;if(x!==null){for(var k=0;k<x.length;k++){var T=x[k];for(q=T;q!==null;){var W=q;switch(W.tag){case 0:case 11:case 15:co(8,W,a)}var H=W.child;if(H!==null)H.return=W,q=H;else for(;q!==null;){W=q;var V=W.sibling,$=W.return;if(af(W),W===T){q=null;break}if(V!==null){V.return=$,q=V;break}q=$}}}var ee=a.alternate;if(ee!==null){var ne=ee.child;if(ne!==null){ee.child=null;do{var Le=ne.sibling;ne.sibling=null,ne=Le}while(ne!==null)}}q=a}}if((a.subtreeFlags&2064)!==0&&p!==null)p.return=a,q=p;else e:for(;q!==null;){if(a=q,(a.flags&2048)!==0)switch(a.tag){case 0:case 11:case 15:co(9,a,a.return)}var D=a.sibling;if(D!==null){D.return=a.return,q=D;break e}q=a.return}}var N=e.current;for(q=N;q!==null;){p=q;var O=p.child;if((p.subtreeFlags&2064)!==0&&O!==null)O.return=p,q=O;else e:for(p=N;q!==null;){if(x=q,(x.flags&2048)!==0)try{switch(x.tag){case 0:case 11:case 15:vl(9,x)}}catch(re){Te(x,x.return,re)}if(x===p){q=null;break e}var K=x.sibling;if(K!==null){K.return=x.return,q=K;break e}q=x.return}}if(Ce=i,mn(),jt&&typeof jt.onPostCommitFiberRoot=="function")try{jt.onPostCommitFiberRoot(Do,e)}catch{}o=!0}return o}finally{ke=n,At.transition=t}}return!1}function Ef(e,t,n){t=fr(n,t),t=Qc(e,t,1),e=vn(e,t,1),t=qe(),e!==null&&(Fr(e,1,t),ot(e,t))}function Te(e,t,n){if(e.tag===3)Ef(e,e,n);else for(;t!==null;){if(t.tag===3){Ef(t,e,n);break}else if(t.tag===1){var o=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof o.componentDidCatch=="function"&&(An===null||!An.has(o))){e=fr(n,e),e=Vc(t,e,1),t=vn(t,e,1),e=qe(),t!==null&&(Fr(t,1,e),ot(t,e));break}}t=t.return}}function Pm(e,t,n){var o=e.pingCache;o!==null&&o.delete(t),t=qe(),e.pingedLanes|=e.suspendedLanes&n,Ue===e&&(We&n)===n&&(_e===4||_e===3&&(We&130023424)===We&&500>je()-js?Un(e,0):Ms|=n),ot(e,t)}function kf(e,t){t===0&&((e.mode&1)===0?t=1:(t=To,To<<=1,(To&130023424)===0&&(To=4194304)));var n=qe();e=Xt(e,t),e!==null&&(Fr(e,t,n),ot(e,n))}function Rm(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),kf(e,n)}function Im(e,t){var n=0;switch(e.tag){case 13:var o=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:o=e.stateNode;break;default:throw Error(s(314))}o!==null&&o.delete(t),kf(e,n)}var Nf;Nf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||et.current)nt=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return nt=!1,gm(e,t,n);nt=(e.flags&131072)!==0}else nt=!1,Ie&&(t.flags&1048576)!==0&&oc(t,el,t.index);switch(t.lanes=0,t.tag){case 2:var o=t.type;ml(e,t),e=t.pendingProps;var i=rr(t,be.current);ur(t,n),i=fs(null,t,o,e,i,n);var a=ds();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,tt(o)?(a=!0,$o(t)):a=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,os(t),i.updater=pl,t.stateNode=i,i._reactInternals=t,ys(t,o,e,n),t=Cs(null,t,o,!0,a,n)):(t.tag=0,Ie&&a&&bi(t),Ze(null,t,i,n),t=t.child),t;case 16:o=t.elementType;e:{switch(ml(e,t),e=t.pendingProps,i=o._init,o=i(o._payload),t.type=o,i=t.tag=Om(o),e=Nt(o,e),i){case 0:t=xs(null,t,o,e,n);break e;case 1:t=$c(null,t,o,e,n);break e;case 11:t=Kc(null,t,o,e,n);break e;case 14:t=Gc(null,t,o,Nt(o.type,e),n);break e}throw Error(s(306,o,""))}return t;case 0:return o=t.type,i=t.pendingProps,i=t.elementType===o?i:Nt(o,i),xs(e,t,o,i,n);case 1:return o=t.type,i=t.pendingProps,i=t.elementType===o?i:Nt(o,i),$c(e,t,o,i,n);case 3:e:{if(Zc(t),e===null)throw Error(s(387));o=t.pendingProps,a=t.memoizedState,i=a.element,pc(e,t),il(t,o,null,n);var p=t.memoizedState;if(o=p.element,a.isDehydrated)if(a={element:o,isDehydrated:!1,cache:p.cache,pendingSuspenseBoundaries:p.pendingSuspenseBoundaries,transitions:p.transitions},t.updateQueue.baseState=a,t.memoizedState=a,t.flags&256){i=fr(Error(s(423)),t),t=qc(e,t,o,n,i);break e}else if(o!==i){i=fr(Error(s(424)),t),t=qc(e,t,o,n,i);break e}else for(ut=dn(t.stateNode.containerInfo.firstChild),at=t,Ie=!0,kt=null,n=fc(t,null,o,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ir(),o===i){t=Zt(e,t,n);break e}Ze(e,t,o,n)}t=t.child}return t;case 5:return gc(t),e===null&&$i(t),o=t.type,i=t.pendingProps,a=e!==null?e.memoizedProps:null,p=i.children,Vi(o,i)?p=null:a!==null&&Vi(o,a)&&(t.flags|=32),Xc(e,t),Ze(e,t,p,n),t.child;case 6:return e===null&&$i(t),null;case 13:return ef(e,t,n);case 4:return ls(t,t.stateNode.containerInfo),o=t.pendingProps,e===null?t.child=sr(t,null,o,n):Ze(e,t,o,n),t.child;case 11:return o=t.type,i=t.pendingProps,i=t.elementType===o?i:Nt(o,i),Kc(e,t,o,i,n);case 7:return Ze(e,t,t.pendingProps,n),t.child;case 8:return Ze(e,t,t.pendingProps.children,n),t.child;case 12:return Ze(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(o=t.type._context,i=t.pendingProps,a=t.memoizedProps,p=i.value,Ne(rl,o._currentValue),o._currentValue=p,a!==null)if(Et(a.value,p)){if(a.children===i.children&&!et.current){t=Zt(e,t,n);break e}}else for(a=t.child,a!==null&&(a.return=t);a!==null;){var x=a.dependencies;if(x!==null){p=a.child;for(var k=x.firstContext;k!==null;){if(k.context===o){if(a.tag===1){k=$t(-1,n&-n),k.tag=2;var T=a.updateQueue;if(T!==null){T=T.shared;var W=T.pending;W===null?k.next=k:(k.next=W.next,W.next=k),T.pending=k}}a.lanes|=n,k=a.alternate,k!==null&&(k.lanes|=n),ns(a.return,n,t),x.lanes|=n;break}k=k.next}}else if(a.tag===10)p=a.type===t.type?null:a.child;else if(a.tag===18){if(p=a.return,p===null)throw Error(s(341));p.lanes|=n,x=p.alternate,x!==null&&(x.lanes|=n),ns(p,n,t),p=a.sibling}else p=a.child;if(p!==null)p.return=a;else for(p=a;p!==null;){if(p===t){p=null;break}if(a=p.sibling,a!==null){a.return=p.return,p=a;break}p=p.return}a=p}Ze(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,o=t.pendingProps.children,ur(t,n),i=vt(i),o=o(i),t.flags|=1,Ze(e,t,o,n),t.child;case 14:return o=t.type,i=Nt(o,t.pendingProps),i=Nt(o.type,i),Gc(e,t,o,i,n);case 15:return bc(e,t,t.type,t.pendingProps,n);case 17:return o=t.type,i=t.pendingProps,i=t.elementType===o?i:Nt(o,i),ml(e,t),t.tag=1,tt(o)?(e=!0,$o(t)):e=!1,ur(t,n),zc(t,o,i),ys(t,o,i,n),Cs(null,t,o,!0,e,n);case 19:return nf(e,t,n);case 22:return Jc(e,t,n)}throw Error(s(156,t.tag))};function Pf(e,t){return lu(e,t)}function Dm(e,t,n,o){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function wt(e,t,n,o){return new Dm(e,t,n,o)}function Vs(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Om(e){if(typeof e=="function")return Vs(e)?1:0;if(e!=null){if(e=e.$$typeof,e===xe)return 11;if(e===Z)return 14}return 2}function Sn(e,t){var n=e.alternate;return n===null?(n=wt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Nl(e,t,n,o,i,a){var p=2;if(o=e,typeof e=="function")Vs(e)&&(p=1);else if(typeof e=="string")p=5;else e:switch(e){case U:return Vn(n.children,i,a,t);case J:p=8,i|=8;break;case X:return e=wt(12,n,t,i|2),e.elementType=X,e.lanes=a,e;case ye:return e=wt(13,n,t,i),e.elementType=ye,e.lanes=a,e;case Y:return e=wt(19,n,t,i),e.elementType=Y,e.lanes=a,e;case te:return Pl(n,i,a,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case oe:p=10;break e;case me:p=9;break e;case xe:p=11;break e;case Z:p=14;break e;case le:p=16,o=null;break e}throw Error(s(130,e==null?e:typeof e,""))}return t=wt(p,n,t,i),t.elementType=e,t.type=o,t.lanes=a,t}function Vn(e,t,n,o){return e=wt(7,e,o,t),e.lanes=n,e}function Pl(e,t,n,o){return e=wt(22,e,o,t),e.elementType=te,e.lanes=n,e.stateNode={isHidden:!1},e}function Ws(e,t,n){return e=wt(6,e,null,t),e.lanes=n,e}function Hs(e,t,n){return t=wt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Tm(e,t,n,o,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vi(0),this.expirationTimes=vi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vi(0),this.identifierPrefix=o,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Ys(e,t,n,o,i,a,p,x,k){return e=new Tm(e,t,n,x,k),t===1?(t=1,a===!0&&(t|=8)):t=0,a=wt(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:o,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},os(a),e}function Mm(e,t,n){var o=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:z,key:o==null?null:""+o,children:e,containerInfo:t,implementation:n}}function Rf(e){if(!e)return hn;e=e._reactInternals;e:{if(Dn(e)!==e||e.tag!==1)throw Error(s(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(tt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(s(171))}if(e.tag===1){var n=e.type;if(tt(n))return tc(e,n,t)}return t}function If(e,t,n,o,i,a,p,x,k){return e=Ys(n,o,!0,e,i,a,p,x,k),e.context=Rf(null),n=e.current,o=qe(),i=xn(n),a=$t(o,i),a.callback=t??null,vn(n,a,i),e.current.lanes=i,Fr(e,i,o),ot(e,o),e}function Rl(e,t,n,o){var i=t.current,a=qe(),p=xn(i);return n=Rf(n),t.context===null?t.context=n:t.pendingContext=n,t=$t(a,p),t.payload={element:e},o=o===void 0?null:o,o!==null&&(t.callback=o),e=vn(i,t,p),e!==null&&(It(e,i,p,a),ll(e,i,p)),p}function Il(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Df(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ks(e,t){Df(e,t),(e=e.alternate)&&Df(e,t)}function jm(){return null}var Of=typeof reportError=="function"?reportError:function(e){console.error(e)};function Gs(e){this._internalRoot=e}Dl.prototype.render=Gs.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));Rl(e,t,null,null)},Dl.prototype.unmount=Gs.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;zn(function(){Rl(null,e,null,null)}),t[Kt]=null}};function Dl(e){this._internalRoot=e}Dl.prototype.unstable_scheduleHydration=function(e){if(e){var t=pu();e={blockedOn:null,target:e,priority:t};for(var n=0;n<un.length&&t!==0&&t<un[n].priority;n++);un.splice(n,0,e),n===0&&gu(e)}};function bs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ol(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Tf(){}function Lm(e,t,n,o,i){if(i){if(typeof o=="function"){var a=o;o=function(){var T=Il(p);a.call(T)}}var p=If(t,o,e,0,null,!1,!1,"",Tf);return e._reactRootContainer=p,e[Kt]=p.current,$r(e.nodeType===8?e.parentNode:e),zn(),p}for(;i=e.lastChild;)e.removeChild(i);if(typeof o=="function"){var x=o;o=function(){var T=Il(k);x.call(T)}}var k=Ys(e,0,!1,null,null,!1,!1,"",Tf);return e._reactRootContainer=k,e[Kt]=k.current,$r(e.nodeType===8?e.parentNode:e),zn(function(){Rl(t,k,n,o)}),k}function Tl(e,t,n,o,i){var a=n._reactRootContainer;if(a){var p=a;if(typeof i=="function"){var x=i;i=function(){var k=Il(p);x.call(k)}}Rl(t,p,e,i)}else p=Lm(n,t,e,i,o);return Il(p)}fu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Br(t.pendingLanes);n!==0&&(yi(t,n|1),ot(t,je()),(Ce&6)===0&&(hr=je()+500,mn()))}break;case 13:zn(function(){var o=Xt(e,1);if(o!==null){var i=qe();It(o,e,1,i)}}),Ks(e,1)}},Ai=function(e){if(e.tag===13){var t=Xt(e,134217728);if(t!==null){var n=qe();It(t,e,134217728,n)}Ks(e,134217728)}},du=function(e){if(e.tag===13){var t=xn(e),n=Xt(e,t);if(n!==null){var o=qe();It(n,e,t,o)}Ks(e,t)}},pu=function(){return ke},hu=function(e,t){var n=ke;try{return ke=e,t()}finally{ke=n}},fi=function(e,t,n){switch(t){case"input":if(rn(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var o=n[t];if(o!==e&&o.form===e.form){var i=Jo(o);if(!i)throw Error(s(90));ie(o),rn(o,i)}}}break;case"textarea":Wa(e,n);break;case"select":t=n.value,t!=null&&Ct(e,!!n.multiple,t,!1)}},Za=zs,qa=zn;var Bm={usingClientEntryPoint:!1,Events:[eo,tr,Jo,Xa,$a,zs]},mo={findFiberByHostInstance:On,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Fm={bundleType:mo.bundleType,version:mo.version,rendererPackageName:mo.rendererPackageName,rendererConfig:mo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:F.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ru(e),e===null?null:e.stateNode},findFiberByHostInstance:mo.findFiberByHostInstance||jm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ml=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ml.isDisabled&&Ml.supportsFiber)try{Do=Ml.inject(Fm),jt=Ml}catch{}}return lt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Bm,lt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!bs(t))throw Error(s(200));return Mm(e,t,null,n)},lt.createRoot=function(e,t){if(!bs(e))throw Error(s(299));var n=!1,o="",i=Of;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(o=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Ys(e,1,!1,null,null,n,!1,o,i),e[Kt]=t.current,$r(e.nodeType===8?e.parentNode:e),new Gs(t)},lt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=ru(t),e=e===null?null:e.stateNode,e},lt.flushSync=function(e){return zn(e)},lt.hydrate=function(e,t,n){if(!Ol(t))throw Error(s(200));return Tl(null,e,t,!0,n)},lt.hydrateRoot=function(e,t,n){if(!bs(e))throw Error(s(405));var o=n!=null&&n.hydratedSources||null,i=!1,a="",p=Of;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(a=n.identifierPrefix),n.onRecoverableError!==void 0&&(p=n.onRecoverableError)),t=If(t,null,e,1,n??null,i,!1,a,p),e[Kt]=t.current,$r(e),o)for(e=0;e<o.length;e++)n=o[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Dl(t)},lt.render=function(e,t,n){if(!Ol(t))throw Error(s(200));return Tl(null,e,t,!1,n)},lt.unmountComponentAtNode=function(e){if(!Ol(e))throw Error(s(40));return e._reactRootContainer?(zn(function(){Tl(null,null,e,!1,function(){e._reactRootContainer=null,e[Kt]=null})}),!0):!1},lt.unstable_batchedUpdates=zs,lt.unstable_renderSubtreeIntoContainer=function(e,t,n,o){if(!Ol(n))throw Error(s(200));if(e==null||e._reactInternals===void 0)throw Error(s(38));return Tl(e,t,n,!1,o)},lt.version="18.3.1-next-f1338f8080-20240426",lt}var Uf;function Cd(){if(Uf)return $s.exports;Uf=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(l){console.error(l)}}return r(),$s.exports=Ym(),$s.exports}var Qf;function Km(){if(Qf)return jl;Qf=1;var r=Cd();return jl.createRoot=r.createRoot,jl.hydrateRoot=r.hydrateRoot,jl}var Gm=Km();const bm=[{key:"start",label:"开始"},{key:"style",label:"样式"},{key:"insert",label:"插入"},{key:"view",label:"视图"},{key:"export",label:"导出"}];function Jm({activeMenu:r,onMenuChange:l}){const s=u=>{r!==u&&l(u)};return v.jsx("div",{className:"top-menu",children:v.jsx("div",{className:"menu-container",children:v.jsx("div",{className:"menu-items",children:bm.map(u=>v.jsx("button",{type:"button",onClick:()=>s(u.key),className:`menu-item ${r===u.key?"active":""}`,children:u.label},u.key))})})})}/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xm=r=>r.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Sd=(...r)=>r.filter((l,s,u)=>!!l&&l.trim()!==""&&u.indexOf(l)===s).join(" ").trim();/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var $m={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zm=w.forwardRef(({color:r="currentColor",size:l=24,strokeWidth:s=2,absoluteStrokeWidth:u,className:c="",children:f,iconNode:d,...h},m)=>w.createElement("svg",{ref:m,...$m,width:l,height:l,stroke:r,strokeWidth:u?Number(s)*24/Number(l):s,className:Sd("lucide",c),...h},[...d.map(([g,y])=>w.createElement(g,y)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wt=(r,l)=>{const s=w.forwardRef(({className:u,...c},f)=>w.createElement(Zm,{ref:f,iconNode:l,className:Sd(`lucide-${Xm(r)}`,u),...c}));return s.displayName=`${r}`,s};/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qm=[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]],Ed=Wt("Bold",qm);/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eg=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],vo=Wt("ChevronDown",eg);/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tg=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],kd=Wt("ChevronRight",tg);/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ng=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Vf=Wt("ChevronUp",ng);/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rg=[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]],Nd=Wt("Italic",rg);/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const og=[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]],lg=Wt("ListOrdered",og);/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ig=[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]],sg=Wt("List",ig);/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ag=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],ug=Wt("Plus",ag);/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cg=[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]],fg=Wt("Strikethrough",cg);/**
 * @license lucide-react v0.471.2 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dg=[["path",{d:"M6 4v6a6 6 0 0 0 12 0V4",key:"9kb039"}],["line",{x1:"4",x2:"20",y1:"20",y2:"20",key:"nun2al"}]],pg=Wt("Underline",dg);function Wf(r,l){if(typeof r=="function")return r(l);r!=null&&(r.current=l)}function Pd(...r){return l=>{let s=!1;const u=r.map(c=>{const f=Wf(c,l);return!s&&typeof f=="function"&&(s=!0),f});if(s)return()=>{for(let c=0;c<u.length;c++){const f=u[c];typeof f=="function"?f():Wf(r[c],null)}}}}function Ht(...r){return w.useCallback(Pd(...r),r)}function Ea(r){const l=mg(r),s=w.forwardRef((u,c)=>{const{children:f,...d}=u,h=w.Children.toArray(f),m=h.find(vg);if(m){const g=m.props.children,y=h.map(A=>A===m?w.Children.count(g)>1?w.Children.only(null):w.isValidElement(g)?g.props.children:null:A);return v.jsx(l,{...d,ref:c,children:w.isValidElement(g)?w.cloneElement(g,void 0,y):null})}return v.jsx(l,{...d,ref:c,children:f})});return s.displayName=`${r}.Slot`,s}var hg=Ea("Slot");function mg(r){const l=w.forwardRef((s,u)=>{const{children:c,...f}=s;if(w.isValidElement(c)){const d=Ag(c),h=yg(f,c.props);return c.type!==w.Fragment&&(h.ref=u?Pd(u,d):d),w.cloneElement(c,h)}return w.Children.count(c)>1?w.Children.only(null):null});return l.displayName=`${r}.SlotClone`,l}var Rd=Symbol("radix.slottable");function gg(r){const l=({children:s})=>v.jsx(v.Fragment,{children:s});return l.displayName=`${r}.Slottable`,l.__radixId=Rd,l}function vg(r){return w.isValidElement(r)&&typeof r.type=="function"&&"__radixId"in r.type&&r.type.__radixId===Rd}function yg(r,l){const s={...l};for(const u in l){const c=r[u],f=l[u];/^on[A-Z]/.test(u)?c&&f?s[u]=(...h)=>{const m=f(...h);return c(...h),m}:c&&(s[u]=c):u==="style"?s[u]={...c,...f}:u==="className"&&(s[u]=[c,f].filter(Boolean).join(" "))}return{...r,...s}}function Ag(r){var u,c;let l=(u=Object.getOwnPropertyDescriptor(r.props,"ref"))==null?void 0:u.get,s=l&&"isReactWarning"in l&&l.isReactWarning;return s?r.ref:(l=(c=Object.getOwnPropertyDescriptor(r,"ref"))==null?void 0:c.get,s=l&&"isReactWarning"in l&&l.isReactWarning,s?r.props.ref:r.props.ref||r.ref)}const Ye=w.forwardRef(({className:r="",variant:l="default",size:s="default",asChild:u=!1,...c},f)=>{const d=u?hg:"button",h=()=>{let m="btn";switch(l){case"default":m+=" btn-default";break;case"destructive":m+=" btn-destructive";break;case"outline":m+=" btn-outline";break;case"secondary":m+=" btn-secondary";break;case"ghost":m+=" btn-ghost";break;case"link":m+=" btn-link";break}switch(s){case"default":m+=" btn-default-size";break;case"sm":m+=" btn-sm";break;case"lg":m+=" btn-lg";break;case"icon":m+=" btn-icon";break}return r&&(m+=` ${r}`),m};return v.jsx(d,{className:h(),ref:f,...c})});Ye.displayName="Button";var ka=Cd();const wg=wd(ka);var xg=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],xt=xg.reduce((r,l)=>{const s=Ea(`Primitive.${l}`),u=w.forwardRef((c,f)=>{const{asChild:d,...h}=c,m=d?s:l;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),v.jsx(m,{...h,ref:f})});return u.displayName=`Primitive.${l}`,{...r,[l]:u}},{});function Cg(r,l){r&&ka.flushSync(()=>r.dispatchEvent(l))}var Sg="Separator",Hf="horizontal",Eg=["horizontal","vertical"],Id=w.forwardRef((r,l)=>{const{decorative:s,orientation:u=Hf,...c}=r,f=kg(u)?u:Hf,h=s?{role:"none"}:{"aria-orientation":f==="vertical"?f:void 0,role:"separator"};return v.jsx(xt.div,{"data-orientation":f,...h,...c,ref:l})});Id.displayName=Sg;function kg(r){return Eg.includes(r)}var Dd=Id;const Ar=w.forwardRef(({className:r="",orientation:l="horizontal",decorative:s=!0,...u},c)=>{const f=()=>{let d="separator";return l==="horizontal"?d+=" separator-horizontal":d+=" separator-vertical",r&&(d+=` ${r}`),d};return v.jsx(Dd,{ref:c,decorative:s,orientation:l,className:f(),...u})});Ar.displayName=Dd.displayName;function it(r,l,{checkForDefaultPrevented:s=!0}={}){return function(c){if(r==null||r(c),s===!1||!c.defaultPrevented)return l==null?void 0:l(c)}}function Na(r,l=[]){let s=[];function u(f,d){const h=w.createContext(d),m=s.length;s=[...s,d];const g=A=>{var j;const{scope:C,children:S,...R}=A,E=((j=C==null?void 0:C[r])==null?void 0:j[m])||h,I=w.useMemo(()=>R,Object.values(R));return v.jsx(E.Provider,{value:I,children:S})};g.displayName=f+"Provider";function y(A,C){var E;const S=((E=C==null?void 0:C[r])==null?void 0:E[m])||h,R=w.useContext(S);if(R)return R;if(d!==void 0)return d;throw new Error(`\`${A}\` must be used within \`${f}\``)}return[g,y]}const c=()=>{const f=s.map(d=>w.createContext(d));return function(h){const m=(h==null?void 0:h[r])||f;return w.useMemo(()=>({[`__scope${r}`]:{...h,[r]:m}}),[h,m])}};return c.scopeName=r,[u,Ng(c,...l)]}function Ng(...r){const l=r[0];if(r.length===1)return l;const s=()=>{const u=r.map(c=>({useScope:c(),scopeName:c.scopeName}));return function(f){const d=u.reduce((h,{useScope:m,scopeName:g})=>{const A=m(f)[`__scope${g}`];return{...h,...A}},{});return w.useMemo(()=>({[`__scope${l.scopeName}`]:d}),[d])}};return s.scopeName=l.scopeName,s}function kr(r){const l=w.useRef(r);return w.useEffect(()=>{l.current=r}),w.useMemo(()=>(...s)=>{var u;return(u=l.current)==null?void 0:u.call(l,...s)},[])}function Pg(r,l=globalThis==null?void 0:globalThis.document){const s=kr(r);w.useEffect(()=>{const u=c=>{c.key==="Escape"&&s(c)};return l.addEventListener("keydown",u,{capture:!0}),()=>l.removeEventListener("keydown",u,{capture:!0})},[s,l])}var Rg="DismissableLayer",fa="dismissableLayer.update",Ig="dismissableLayer.pointerDownOutside",Dg="dismissableLayer.focusOutside",Yf,Od=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Pa=w.forwardRef((r,l)=>{const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:d,onDismiss:h,...m}=r,g=w.useContext(Od),[y,A]=w.useState(null),C=(y==null?void 0:y.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,S]=w.useState({}),R=Ht(l,U=>A(U)),E=Array.from(g.layers),[I]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),j=E.indexOf(I),B=y?E.indexOf(y):-1,L=g.layersWithOutsidePointerEventsDisabled.size>0,F=B>=j,M=Mg(U=>{const J=U.target,X=[...g.branches].some(oe=>oe.contains(J));!F||X||(c==null||c(U),d==null||d(U),U.defaultPrevented||h==null||h())},C),z=jg(U=>{const J=U.target;[...g.branches].some(oe=>oe.contains(J))||(f==null||f(U),d==null||d(U),U.defaultPrevented||h==null||h())},C);return Pg(U=>{B===g.layers.size-1&&(u==null||u(U),!U.defaultPrevented&&h&&(U.preventDefault(),h()))},C),w.useEffect(()=>{if(y)return s&&(g.layersWithOutsidePointerEventsDisabled.size===0&&(Yf=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(y)),g.layers.add(y),Kf(),()=>{s&&g.layersWithOutsidePointerEventsDisabled.size===1&&(C.body.style.pointerEvents=Yf)}},[y,C,s,g]),w.useEffect(()=>()=>{y&&(g.layers.delete(y),g.layersWithOutsidePointerEventsDisabled.delete(y),Kf())},[y,g]),w.useEffect(()=>{const U=()=>S({});return document.addEventListener(fa,U),()=>document.removeEventListener(fa,U)},[]),v.jsx(xt.div,{...m,ref:R,style:{pointerEvents:L?F?"auto":"none":void 0,...r.style},onFocusCapture:it(r.onFocusCapture,z.onFocusCapture),onBlurCapture:it(r.onBlurCapture,z.onBlurCapture),onPointerDownCapture:it(r.onPointerDownCapture,M.onPointerDownCapture)})});Pa.displayName=Rg;var Og="DismissableLayerBranch",Tg=w.forwardRef((r,l)=>{const s=w.useContext(Od),u=w.useRef(null),c=Ht(l,u);return w.useEffect(()=>{const f=u.current;if(f)return s.branches.add(f),()=>{s.branches.delete(f)}},[s.branches]),v.jsx(xt.div,{...r,ref:c})});Tg.displayName=Og;function Mg(r,l=globalThis==null?void 0:globalThis.document){const s=kr(r),u=w.useRef(!1),c=w.useRef(()=>{});return w.useEffect(()=>{const f=h=>{if(h.target&&!u.current){let m=function(){Td(Ig,s,g,{discrete:!0})};const g={originalEvent:h};h.pointerType==="touch"?(l.removeEventListener("click",c.current),c.current=m,l.addEventListener("click",c.current,{once:!0})):m()}else l.removeEventListener("click",c.current);u.current=!1},d=window.setTimeout(()=>{l.addEventListener("pointerdown",f)},0);return()=>{window.clearTimeout(d),l.removeEventListener("pointerdown",f),l.removeEventListener("click",c.current)}},[l,s]),{onPointerDownCapture:()=>u.current=!0}}function jg(r,l=globalThis==null?void 0:globalThis.document){const s=kr(r),u=w.useRef(!1);return w.useEffect(()=>{const c=f=>{f.target&&!u.current&&Td(Dg,s,{originalEvent:f},{discrete:!1})};return l.addEventListener("focusin",c),()=>l.removeEventListener("focusin",c)},[l,s]),{onFocusCapture:()=>u.current=!0,onBlurCapture:()=>u.current=!1}}function Kf(){const r=new CustomEvent(fa);document.dispatchEvent(r)}function Td(r,l,s,{discrete:u}){const c=s.originalEvent.target,f=new CustomEvent(r,{bubbles:!1,cancelable:!0,detail:s});l&&c.addEventListener(r,l,{once:!0}),u?Cg(c,f):c.dispatchEvent(f)}var ea=0;function Lg(){w.useEffect(()=>{const r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",r[0]??Gf()),document.body.insertAdjacentElement("beforeend",r[1]??Gf()),ea++,()=>{ea===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(l=>l.remove()),ea--}},[])}function Gf(){const r=document.createElement("span");return r.setAttribute("data-radix-focus-guard",""),r.tabIndex=0,r.style.outline="none",r.style.opacity="0",r.style.position="fixed",r.style.pointerEvents="none",r}var ta="focusScope.autoFocusOnMount",na="focusScope.autoFocusOnUnmount",bf={bubbles:!1,cancelable:!0},Bg="FocusScope",Md=w.forwardRef((r,l)=>{const{loop:s=!1,trapped:u=!1,onMountAutoFocus:c,onUnmountAutoFocus:f,...d}=r,[h,m]=w.useState(null),g=kr(c),y=kr(f),A=w.useRef(null),C=Ht(l,E=>m(E)),S=w.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;w.useEffect(()=>{if(u){let E=function(L){if(S.paused||!h)return;const F=L.target;h.contains(F)?A.current=F:kn(A.current,{select:!0})},I=function(L){if(S.paused||!h)return;const F=L.relatedTarget;F!==null&&(h.contains(F)||kn(A.current,{select:!0}))},j=function(L){if(document.activeElement===document.body)for(const M of L)M.removedNodes.length>0&&kn(h)};document.addEventListener("focusin",E),document.addEventListener("focusout",I);const B=new MutationObserver(j);return h&&B.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",E),document.removeEventListener("focusout",I),B.disconnect()}}},[u,h,S.paused]),w.useEffect(()=>{if(h){Xf.add(S);const E=document.activeElement;if(!h.contains(E)){const j=new CustomEvent(ta,bf);h.addEventListener(ta,g),h.dispatchEvent(j),j.defaultPrevented||(Fg(Vg(jd(h)),{select:!0}),document.activeElement===E&&kn(h))}return()=>{h.removeEventListener(ta,g),setTimeout(()=>{const j=new CustomEvent(na,bf);h.addEventListener(na,y),h.dispatchEvent(j),j.defaultPrevented||kn(E??document.body,{select:!0}),h.removeEventListener(na,y),Xf.remove(S)},0)}}},[h,g,y,S]);const R=w.useCallback(E=>{if(!s&&!u||S.paused)return;const I=E.key==="Tab"&&!E.altKey&&!E.ctrlKey&&!E.metaKey,j=document.activeElement;if(I&&j){const B=E.currentTarget,[L,F]=_g(B);L&&F?!E.shiftKey&&j===F?(E.preventDefault(),s&&kn(L,{select:!0})):E.shiftKey&&j===L&&(E.preventDefault(),s&&kn(F,{select:!0})):j===B&&E.preventDefault()}},[s,u,S.paused]);return v.jsx(xt.div,{tabIndex:-1,...d,ref:C,onKeyDown:R})});Md.displayName=Bg;function Fg(r,{select:l=!1}={}){const s=document.activeElement;for(const u of r)if(kn(u,{select:l}),document.activeElement!==s)return}function _g(r){const l=jd(r),s=Jf(l,r),u=Jf(l.reverse(),r);return[s,u]}function jd(r){const l=[],s=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT,{acceptNode:u=>{const c=u.tagName==="INPUT"&&u.type==="hidden";return u.disabled||u.hidden||c?NodeFilter.FILTER_SKIP:u.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)l.push(s.currentNode);return l}function Jf(r,l){for(const s of r)if(!zg(s,{upTo:l}))return s}function zg(r,{upTo:l}){if(getComputedStyle(r).visibility==="hidden")return!0;for(;r;){if(l!==void 0&&r===l)return!1;if(getComputedStyle(r).display==="none")return!0;r=r.parentElement}return!1}function Ug(r){return r instanceof HTMLInputElement&&"select"in r}function kn(r,{select:l=!1}={}){if(r&&r.focus){const s=document.activeElement;r.focus({preventScroll:!0}),r!==s&&Ug(r)&&l&&r.select()}}var Xf=Qg();function Qg(){let r=[];return{add(l){const s=r[0];l!==s&&(s==null||s.pause()),r=$f(r,l),r.unshift(l)},remove(l){var s;r=$f(r,l),(s=r[0])==null||s.resume()}}}function $f(r,l){const s=[...r],u=s.indexOf(l);return u!==-1&&s.splice(u,1),s}function Vg(r){return r.filter(l=>l.tagName!=="A")}var Nn=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},Wg=xd[" useId ".trim().toString()]||(()=>{}),Hg=0;function Ld(r){const[l,s]=w.useState(Wg());return Nn(()=>{s(u=>u??String(Hg++))},[r]),r||(l?`radix-${l}`:"")}const Yg=["top","right","bottom","left"],Pn=Math.min,ft=Math.max,Gl=Math.round,Ll=Math.floor,Qt=r=>({x:r,y:r}),Kg={left:"right",right:"left",bottom:"top",top:"bottom"},Gg={start:"end",end:"start"};function da(r,l,s){return ft(r,Pn(l,s))}function tn(r,l){return typeof r=="function"?r(l):r}function nn(r){return r.split("-")[0]}function Ir(r){return r.split("-")[1]}function Ra(r){return r==="x"?"y":"x"}function Ia(r){return r==="y"?"height":"width"}const bg=new Set(["top","bottom"]);function Ut(r){return bg.has(nn(r))?"y":"x"}function Da(r){return Ra(Ut(r))}function Jg(r,l,s){s===void 0&&(s=!1);const u=Ir(r),c=Da(r),f=Ia(c);let d=c==="x"?u===(s?"end":"start")?"right":"left":u==="start"?"bottom":"top";return l.reference[f]>l.floating[f]&&(d=bl(d)),[d,bl(d)]}function Xg(r){const l=bl(r);return[pa(r),l,pa(l)]}function pa(r){return r.replace(/start|end/g,l=>Gg[l])}const Zf=["left","right"],qf=["right","left"],$g=["top","bottom"],Zg=["bottom","top"];function qg(r,l,s){switch(r){case"top":case"bottom":return s?l?qf:Zf:l?Zf:qf;case"left":case"right":return l?$g:Zg;default:return[]}}function ev(r,l,s,u){const c=Ir(r);let f=qg(nn(r),s==="start",u);return c&&(f=f.map(d=>d+"-"+c),l&&(f=f.concat(f.map(pa)))),f}function bl(r){return r.replace(/left|right|bottom|top/g,l=>Kg[l])}function tv(r){return{top:0,right:0,bottom:0,left:0,...r}}function Bd(r){return typeof r!="number"?tv(r):{top:r,right:r,bottom:r,left:r}}function Jl(r){const{x:l,y:s,width:u,height:c}=r;return{width:u,height:c,top:s,left:l,right:l+u,bottom:s+c,x:l,y:s}}function ed(r,l,s){let{reference:u,floating:c}=r;const f=Ut(l),d=Da(l),h=Ia(d),m=nn(l),g=f==="y",y=u.x+u.width/2-c.width/2,A=u.y+u.height/2-c.height/2,C=u[h]/2-c[h]/2;let S;switch(m){case"top":S={x:y,y:u.y-c.height};break;case"bottom":S={x:y,y:u.y+u.height};break;case"right":S={x:u.x+u.width,y:A};break;case"left":S={x:u.x-c.width,y:A};break;default:S={x:u.x,y:u.y}}switch(Ir(l)){case"start":S[d]-=C*(s&&g?-1:1);break;case"end":S[d]+=C*(s&&g?-1:1);break}return S}const nv=async(r,l,s)=>{const{placement:u="bottom",strategy:c="absolute",middleware:f=[],platform:d}=s,h=f.filter(Boolean),m=await(d.isRTL==null?void 0:d.isRTL(l));let g=await d.getElementRects({reference:r,floating:l,strategy:c}),{x:y,y:A}=ed(g,u,m),C=u,S={},R=0;for(let E=0;E<h.length;E++){const{name:I,fn:j}=h[E],{x:B,y:L,data:F,reset:M}=await j({x:y,y:A,initialPlacement:u,placement:C,strategy:c,middlewareData:S,rects:g,platform:d,elements:{reference:r,floating:l}});y=B??y,A=L??A,S={...S,[I]:{...S[I],...F}},M&&R<=50&&(R++,typeof M=="object"&&(M.placement&&(C=M.placement),M.rects&&(g=M.rects===!0?await d.getElementRects({reference:r,floating:l,strategy:c}):M.rects),{x:y,y:A}=ed(g,C,m)),E=-1)}return{x:y,y:A,placement:C,strategy:c,middlewareData:S}};async function yo(r,l){var s;l===void 0&&(l={});const{x:u,y:c,platform:f,rects:d,elements:h,strategy:m}=r,{boundary:g="clippingAncestors",rootBoundary:y="viewport",elementContext:A="floating",altBoundary:C=!1,padding:S=0}=tn(l,r),R=Bd(S),I=h[C?A==="floating"?"reference":"floating":A],j=Jl(await f.getClippingRect({element:(s=await(f.isElement==null?void 0:f.isElement(I)))==null||s?I:I.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(h.floating)),boundary:g,rootBoundary:y,strategy:m})),B=A==="floating"?{x:u,y:c,width:d.floating.width,height:d.floating.height}:d.reference,L=await(f.getOffsetParent==null?void 0:f.getOffsetParent(h.floating)),F=await(f.isElement==null?void 0:f.isElement(L))?await(f.getScale==null?void 0:f.getScale(L))||{x:1,y:1}:{x:1,y:1},M=Jl(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:B,offsetParent:L,strategy:m}):B);return{top:(j.top-M.top+R.top)/F.y,bottom:(M.bottom-j.bottom+R.bottom)/F.y,left:(j.left-M.left+R.left)/F.x,right:(M.right-j.right+R.right)/F.x}}const rv=r=>({name:"arrow",options:r,async fn(l){const{x:s,y:u,placement:c,rects:f,platform:d,elements:h,middlewareData:m}=l,{element:g,padding:y=0}=tn(r,l)||{};if(g==null)return{};const A=Bd(y),C={x:s,y:u},S=Da(c),R=Ia(S),E=await d.getDimensions(g),I=S==="y",j=I?"top":"left",B=I?"bottom":"right",L=I?"clientHeight":"clientWidth",F=f.reference[R]+f.reference[S]-C[S]-f.floating[R],M=C[S]-f.reference[S],z=await(d.getOffsetParent==null?void 0:d.getOffsetParent(g));let U=z?z[L]:0;(!U||!await(d.isElement==null?void 0:d.isElement(z)))&&(U=h.floating[L]||f.floating[R]);const J=F/2-M/2,X=U/2-E[R]/2-1,oe=Pn(A[j],X),me=Pn(A[B],X),xe=oe,ye=U-E[R]-me,Y=U/2-E[R]/2+J,Z=da(xe,Y,ye),le=!m.arrow&&Ir(c)!=null&&Y!==Z&&f.reference[R]/2-(Y<xe?oe:me)-E[R]/2<0,te=le?Y<xe?Y-xe:Y-ye:0;return{[S]:C[S]+te,data:{[S]:Z,centerOffset:Y-Z-te,...le&&{alignmentOffset:te}},reset:le}}}),ov=function(r){return r===void 0&&(r={}),{name:"flip",options:r,async fn(l){var s,u;const{placement:c,middlewareData:f,rects:d,initialPlacement:h,platform:m,elements:g}=l,{mainAxis:y=!0,crossAxis:A=!0,fallbackPlacements:C,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:R="none",flipAlignment:E=!0,...I}=tn(r,l);if((s=f.arrow)!=null&&s.alignmentOffset)return{};const j=nn(c),B=Ut(h),L=nn(h)===h,F=await(m.isRTL==null?void 0:m.isRTL(g.floating)),M=C||(L||!E?[bl(h)]:Xg(h)),z=R!=="none";!C&&z&&M.push(...ev(h,E,R,F));const U=[h,...M],J=await yo(l,I),X=[];let oe=((u=f.flip)==null?void 0:u.overflows)||[];if(y&&X.push(J[j]),A){const Y=Jg(c,d,F);X.push(J[Y[0]],J[Y[1]])}if(oe=[...oe,{placement:c,overflows:X}],!X.every(Y=>Y<=0)){var me,xe;const Y=(((me=f.flip)==null?void 0:me.index)||0)+1,Z=U[Y];if(Z&&(!(A==="alignment"?B!==Ut(Z):!1)||oe.every(_=>_.overflows[0]>0&&Ut(_.placement)===B)))return{data:{index:Y,overflows:oe},reset:{placement:Z}};let le=(xe=oe.filter(te=>te.overflows[0]<=0).sort((te,_)=>te.overflows[1]-_.overflows[1])[0])==null?void 0:xe.placement;if(!le)switch(S){case"bestFit":{var ye;const te=(ye=oe.filter(_=>{if(z){const b=Ut(_.placement);return b===B||b==="y"}return!0}).map(_=>[_.placement,_.overflows.filter(b=>b>0).reduce((b,G)=>b+G,0)]).sort((_,b)=>_[1]-b[1])[0])==null?void 0:ye[0];te&&(le=te);break}case"initialPlacement":le=h;break}if(c!==le)return{reset:{placement:le}}}return{}}}};function td(r,l){return{top:r.top-l.height,right:r.right-l.width,bottom:r.bottom-l.height,left:r.left-l.width}}function nd(r){return Yg.some(l=>r[l]>=0)}const lv=function(r){return r===void 0&&(r={}),{name:"hide",options:r,async fn(l){const{rects:s}=l,{strategy:u="referenceHidden",...c}=tn(r,l);switch(u){case"referenceHidden":{const f=await yo(l,{...c,elementContext:"reference"}),d=td(f,s.reference);return{data:{referenceHiddenOffsets:d,referenceHidden:nd(d)}}}case"escaped":{const f=await yo(l,{...c,altBoundary:!0}),d=td(f,s.floating);return{data:{escapedOffsets:d,escaped:nd(d)}}}default:return{}}}}},Fd=new Set(["left","top"]);async function iv(r,l){const{placement:s,platform:u,elements:c}=r,f=await(u.isRTL==null?void 0:u.isRTL(c.floating)),d=nn(s),h=Ir(s),m=Ut(s)==="y",g=Fd.has(d)?-1:1,y=f&&m?-1:1,A=tn(l,r);let{mainAxis:C,crossAxis:S,alignmentAxis:R}=typeof A=="number"?{mainAxis:A,crossAxis:0,alignmentAxis:null}:{mainAxis:A.mainAxis||0,crossAxis:A.crossAxis||0,alignmentAxis:A.alignmentAxis};return h&&typeof R=="number"&&(S=h==="end"?R*-1:R),m?{x:S*y,y:C*g}:{x:C*g,y:S*y}}const sv=function(r){return r===void 0&&(r=0),{name:"offset",options:r,async fn(l){var s,u;const{x:c,y:f,placement:d,middlewareData:h}=l,m=await iv(l,r);return d===((s=h.offset)==null?void 0:s.placement)&&(u=h.arrow)!=null&&u.alignmentOffset?{}:{x:c+m.x,y:f+m.y,data:{...m,placement:d}}}}},av=function(r){return r===void 0&&(r={}),{name:"shift",options:r,async fn(l){const{x:s,y:u,placement:c}=l,{mainAxis:f=!0,crossAxis:d=!1,limiter:h={fn:I=>{let{x:j,y:B}=I;return{x:j,y:B}}},...m}=tn(r,l),g={x:s,y:u},y=await yo(l,m),A=Ut(nn(c)),C=Ra(A);let S=g[C],R=g[A];if(f){const I=C==="y"?"top":"left",j=C==="y"?"bottom":"right",B=S+y[I],L=S-y[j];S=da(B,S,L)}if(d){const I=A==="y"?"top":"left",j=A==="y"?"bottom":"right",B=R+y[I],L=R-y[j];R=da(B,R,L)}const E=h.fn({...l,[C]:S,[A]:R});return{...E,data:{x:E.x-s,y:E.y-u,enabled:{[C]:f,[A]:d}}}}}},uv=function(r){return r===void 0&&(r={}),{options:r,fn(l){const{x:s,y:u,placement:c,rects:f,middlewareData:d}=l,{offset:h=0,mainAxis:m=!0,crossAxis:g=!0}=tn(r,l),y={x:s,y:u},A=Ut(c),C=Ra(A);let S=y[C],R=y[A];const E=tn(h,l),I=typeof E=="number"?{mainAxis:E,crossAxis:0}:{mainAxis:0,crossAxis:0,...E};if(m){const L=C==="y"?"height":"width",F=f.reference[C]-f.floating[L]+I.mainAxis,M=f.reference[C]+f.reference[L]-I.mainAxis;S<F?S=F:S>M&&(S=M)}if(g){var j,B;const L=C==="y"?"width":"height",F=Fd.has(nn(c)),M=f.reference[A]-f.floating[L]+(F&&((j=d.offset)==null?void 0:j[A])||0)+(F?0:I.crossAxis),z=f.reference[A]+f.reference[L]+(F?0:((B=d.offset)==null?void 0:B[A])||0)-(F?I.crossAxis:0);R<M?R=M:R>z&&(R=z)}return{[C]:S,[A]:R}}}},cv=function(r){return r===void 0&&(r={}),{name:"size",options:r,async fn(l){var s,u;const{placement:c,rects:f,platform:d,elements:h}=l,{apply:m=()=>{},...g}=tn(r,l),y=await yo(l,g),A=nn(c),C=Ir(c),S=Ut(c)==="y",{width:R,height:E}=f.floating;let I,j;A==="top"||A==="bottom"?(I=A,j=C===(await(d.isRTL==null?void 0:d.isRTL(h.floating))?"start":"end")?"left":"right"):(j=A,I=C==="end"?"top":"bottom");const B=E-y.top-y.bottom,L=R-y.left-y.right,F=Pn(E-y[I],B),M=Pn(R-y[j],L),z=!l.middlewareData.shift;let U=F,J=M;if((s=l.middlewareData.shift)!=null&&s.enabled.x&&(J=L),(u=l.middlewareData.shift)!=null&&u.enabled.y&&(U=B),z&&!C){const oe=ft(y.left,0),me=ft(y.right,0),xe=ft(y.top,0),ye=ft(y.bottom,0);S?J=R-2*(oe!==0||me!==0?oe+me:ft(y.left,y.right)):U=E-2*(xe!==0||ye!==0?xe+ye:ft(y.top,y.bottom))}await m({...l,availableWidth:J,availableHeight:U});const X=await d.getDimensions(h.floating);return R!==X.width||E!==X.height?{reset:{rects:!0}}:{}}}};function Zl(){return typeof window<"u"}function Dr(r){return _d(r)?(r.nodeName||"").toLowerCase():"#document"}function dt(r){var l;return(r==null||(l=r.ownerDocument)==null?void 0:l.defaultView)||window}function Yt(r){var l;return(l=(_d(r)?r.ownerDocument:r.document)||window.document)==null?void 0:l.documentElement}function _d(r){return Zl()?r instanceof Node||r instanceof dt(r).Node:!1}function Ot(r){return Zl()?r instanceof Element||r instanceof dt(r).Element:!1}function Vt(r){return Zl()?r instanceof HTMLElement||r instanceof dt(r).HTMLElement:!1}function rd(r){return!Zl()||typeof ShadowRoot>"u"?!1:r instanceof ShadowRoot||r instanceof dt(r).ShadowRoot}const fv=new Set(["inline","contents"]);function xo(r){const{overflow:l,overflowX:s,overflowY:u,display:c}=Tt(r);return/auto|scroll|overlay|hidden|clip/.test(l+u+s)&&!fv.has(c)}const dv=new Set(["table","td","th"]);function pv(r){return dv.has(Dr(r))}const hv=[":popover-open",":modal"];function ql(r){return hv.some(l=>{try{return r.matches(l)}catch{return!1}})}const mv=["transform","translate","scale","rotate","perspective"],gv=["transform","translate","scale","rotate","perspective","filter"],vv=["paint","layout","strict","content"];function Oa(r){const l=Ta(),s=Ot(r)?Tt(r):r;return mv.some(u=>s[u]?s[u]!=="none":!1)||(s.containerType?s.containerType!=="normal":!1)||!l&&(s.backdropFilter?s.backdropFilter!=="none":!1)||!l&&(s.filter?s.filter!=="none":!1)||gv.some(u=>(s.willChange||"").includes(u))||vv.some(u=>(s.contain||"").includes(u))}function yv(r){let l=Rn(r);for(;Vt(l)&&!Nr(l);){if(Oa(l))return l;if(ql(l))return null;l=Rn(l)}return null}function Ta(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Av=new Set(["html","body","#document"]);function Nr(r){return Av.has(Dr(r))}function Tt(r){return dt(r).getComputedStyle(r)}function ei(r){return Ot(r)?{scrollLeft:r.scrollLeft,scrollTop:r.scrollTop}:{scrollLeft:r.scrollX,scrollTop:r.scrollY}}function Rn(r){if(Dr(r)==="html")return r;const l=r.assignedSlot||r.parentNode||rd(r)&&r.host||Yt(r);return rd(l)?l.host:l}function zd(r){const l=Rn(r);return Nr(l)?r.ownerDocument?r.ownerDocument.body:r.body:Vt(l)&&xo(l)?l:zd(l)}function Ao(r,l,s){var u;l===void 0&&(l=[]),s===void 0&&(s=!0);const c=zd(r),f=c===((u=r.ownerDocument)==null?void 0:u.body),d=dt(c);if(f){const h=ha(d);return l.concat(d,d.visualViewport||[],xo(c)?c:[],h&&s?Ao(h):[])}return l.concat(c,Ao(c,[],s))}function ha(r){return r.parent&&Object.getPrototypeOf(r.parent)?r.frameElement:null}function Ud(r){const l=Tt(r);let s=parseFloat(l.width)||0,u=parseFloat(l.height)||0;const c=Vt(r),f=c?r.offsetWidth:s,d=c?r.offsetHeight:u,h=Gl(s)!==f||Gl(u)!==d;return h&&(s=f,u=d),{width:s,height:u,$:h}}function Ma(r){return Ot(r)?r:r.contextElement}function Sr(r){const l=Ma(r);if(!Vt(l))return Qt(1);const s=l.getBoundingClientRect(),{width:u,height:c,$:f}=Ud(l);let d=(f?Gl(s.width):s.width)/u,h=(f?Gl(s.height):s.height)/c;return(!d||!Number.isFinite(d))&&(d=1),(!h||!Number.isFinite(h))&&(h=1),{x:d,y:h}}const wv=Qt(0);function Qd(r){const l=dt(r);return!Ta()||!l.visualViewport?wv:{x:l.visualViewport.offsetLeft,y:l.visualViewport.offsetTop}}function xv(r,l,s){return l===void 0&&(l=!1),!s||l&&s!==dt(r)?!1:l}function Hn(r,l,s,u){l===void 0&&(l=!1),s===void 0&&(s=!1);const c=r.getBoundingClientRect(),f=Ma(r);let d=Qt(1);l&&(u?Ot(u)&&(d=Sr(u)):d=Sr(r));const h=xv(f,s,u)?Qd(f):Qt(0);let m=(c.left+h.x)/d.x,g=(c.top+h.y)/d.y,y=c.width/d.x,A=c.height/d.y;if(f){const C=dt(f),S=u&&Ot(u)?dt(u):u;let R=C,E=ha(R);for(;E&&u&&S!==R;){const I=Sr(E),j=E.getBoundingClientRect(),B=Tt(E),L=j.left+(E.clientLeft+parseFloat(B.paddingLeft))*I.x,F=j.top+(E.clientTop+parseFloat(B.paddingTop))*I.y;m*=I.x,g*=I.y,y*=I.x,A*=I.y,m+=L,g+=F,R=dt(E),E=ha(R)}}return Jl({width:y,height:A,x:m,y:g})}function ja(r,l){const s=ei(r).scrollLeft;return l?l.left+s:Hn(Yt(r)).left+s}function Vd(r,l,s){s===void 0&&(s=!1);const u=r.getBoundingClientRect(),c=u.left+l.scrollLeft-(s?0:ja(r,u)),f=u.top+l.scrollTop;return{x:c,y:f}}function Cv(r){let{elements:l,rect:s,offsetParent:u,strategy:c}=r;const f=c==="fixed",d=Yt(u),h=l?ql(l.floating):!1;if(u===d||h&&f)return s;let m={scrollLeft:0,scrollTop:0},g=Qt(1);const y=Qt(0),A=Vt(u);if((A||!A&&!f)&&((Dr(u)!=="body"||xo(d))&&(m=ei(u)),Vt(u))){const S=Hn(u);g=Sr(u),y.x=S.x+u.clientLeft,y.y=S.y+u.clientTop}const C=d&&!A&&!f?Vd(d,m,!0):Qt(0);return{width:s.width*g.x,height:s.height*g.y,x:s.x*g.x-m.scrollLeft*g.x+y.x+C.x,y:s.y*g.y-m.scrollTop*g.y+y.y+C.y}}function Sv(r){return Array.from(r.getClientRects())}function Ev(r){const l=Yt(r),s=ei(r),u=r.ownerDocument.body,c=ft(l.scrollWidth,l.clientWidth,u.scrollWidth,u.clientWidth),f=ft(l.scrollHeight,l.clientHeight,u.scrollHeight,u.clientHeight);let d=-s.scrollLeft+ja(r);const h=-s.scrollTop;return Tt(u).direction==="rtl"&&(d+=ft(l.clientWidth,u.clientWidth)-c),{width:c,height:f,x:d,y:h}}function kv(r,l){const s=dt(r),u=Yt(r),c=s.visualViewport;let f=u.clientWidth,d=u.clientHeight,h=0,m=0;if(c){f=c.width,d=c.height;const g=Ta();(!g||g&&l==="fixed")&&(h=c.offsetLeft,m=c.offsetTop)}return{width:f,height:d,x:h,y:m}}const Nv=new Set(["absolute","fixed"]);function Pv(r,l){const s=Hn(r,!0,l==="fixed"),u=s.top+r.clientTop,c=s.left+r.clientLeft,f=Vt(r)?Sr(r):Qt(1),d=r.clientWidth*f.x,h=r.clientHeight*f.y,m=c*f.x,g=u*f.y;return{width:d,height:h,x:m,y:g}}function od(r,l,s){let u;if(l==="viewport")u=kv(r,s);else if(l==="document")u=Ev(Yt(r));else if(Ot(l))u=Pv(l,s);else{const c=Qd(r);u={x:l.x-c.x,y:l.y-c.y,width:l.width,height:l.height}}return Jl(u)}function Wd(r,l){const s=Rn(r);return s===l||!Ot(s)||Nr(s)?!1:Tt(s).position==="fixed"||Wd(s,l)}function Rv(r,l){const s=l.get(r);if(s)return s;let u=Ao(r,[],!1).filter(h=>Ot(h)&&Dr(h)!=="body"),c=null;const f=Tt(r).position==="fixed";let d=f?Rn(r):r;for(;Ot(d)&&!Nr(d);){const h=Tt(d),m=Oa(d);!m&&h.position==="fixed"&&(c=null),(f?!m&&!c:!m&&h.position==="static"&&!!c&&Nv.has(c.position)||xo(d)&&!m&&Wd(r,d))?u=u.filter(y=>y!==d):c=h,d=Rn(d)}return l.set(r,u),u}function Iv(r){let{element:l,boundary:s,rootBoundary:u,strategy:c}=r;const d=[...s==="clippingAncestors"?ql(l)?[]:Rv(l,this._c):[].concat(s),u],h=d[0],m=d.reduce((g,y)=>{const A=od(l,y,c);return g.top=ft(A.top,g.top),g.right=Pn(A.right,g.right),g.bottom=Pn(A.bottom,g.bottom),g.left=ft(A.left,g.left),g},od(l,h,c));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}}function Dv(r){const{width:l,height:s}=Ud(r);return{width:l,height:s}}function Ov(r,l,s){const u=Vt(l),c=Yt(l),f=s==="fixed",d=Hn(r,!0,f,l);let h={scrollLeft:0,scrollTop:0};const m=Qt(0);function g(){m.x=ja(c)}if(u||!u&&!f)if((Dr(l)!=="body"||xo(c))&&(h=ei(l)),u){const S=Hn(l,!0,f,l);m.x=S.x+l.clientLeft,m.y=S.y+l.clientTop}else c&&g();f&&!u&&c&&g();const y=c&&!u&&!f?Vd(c,h):Qt(0),A=d.left+h.scrollLeft-m.x-y.x,C=d.top+h.scrollTop-m.y-y.y;return{x:A,y:C,width:d.width,height:d.height}}function ra(r){return Tt(r).position==="static"}function ld(r,l){if(!Vt(r)||Tt(r).position==="fixed")return null;if(l)return l(r);let s=r.offsetParent;return Yt(r)===s&&(s=s.ownerDocument.body),s}function Hd(r,l){const s=dt(r);if(ql(r))return s;if(!Vt(r)){let c=Rn(r);for(;c&&!Nr(c);){if(Ot(c)&&!ra(c))return c;c=Rn(c)}return s}let u=ld(r,l);for(;u&&pv(u)&&ra(u);)u=ld(u,l);return u&&Nr(u)&&ra(u)&&!Oa(u)?s:u||yv(r)||s}const Tv=async function(r){const l=this.getOffsetParent||Hd,s=this.getDimensions,u=await s(r.floating);return{reference:Ov(r.reference,await l(r.floating),r.strategy),floating:{x:0,y:0,width:u.width,height:u.height}}};function Mv(r){return Tt(r).direction==="rtl"}const jv={convertOffsetParentRelativeRectToViewportRelativeRect:Cv,getDocumentElement:Yt,getClippingRect:Iv,getOffsetParent:Hd,getElementRects:Tv,getClientRects:Sv,getDimensions:Dv,getScale:Sr,isElement:Ot,isRTL:Mv};function Yd(r,l){return r.x===l.x&&r.y===l.y&&r.width===l.width&&r.height===l.height}function Lv(r,l){let s=null,u;const c=Yt(r);function f(){var h;clearTimeout(u),(h=s)==null||h.disconnect(),s=null}function d(h,m){h===void 0&&(h=!1),m===void 0&&(m=1),f();const g=r.getBoundingClientRect(),{left:y,top:A,width:C,height:S}=g;if(h||l(),!C||!S)return;const R=Ll(A),E=Ll(c.clientWidth-(y+C)),I=Ll(c.clientHeight-(A+S)),j=Ll(y),L={rootMargin:-R+"px "+-E+"px "+-I+"px "+-j+"px",threshold:ft(0,Pn(1,m))||1};let F=!0;function M(z){const U=z[0].intersectionRatio;if(U!==m){if(!F)return d();U?d(!1,U):u=setTimeout(()=>{d(!1,1e-7)},1e3)}U===1&&!Yd(g,r.getBoundingClientRect())&&d(),F=!1}try{s=new IntersectionObserver(M,{...L,root:c.ownerDocument})}catch{s=new IntersectionObserver(M,L)}s.observe(r)}return d(!0),f}function Bv(r,l,s,u){u===void 0&&(u={});const{ancestorScroll:c=!0,ancestorResize:f=!0,elementResize:d=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:m=!1}=u,g=Ma(r),y=c||f?[...g?Ao(g):[],...Ao(l)]:[];y.forEach(j=>{c&&j.addEventListener("scroll",s,{passive:!0}),f&&j.addEventListener("resize",s)});const A=g&&h?Lv(g,s):null;let C=-1,S=null;d&&(S=new ResizeObserver(j=>{let[B]=j;B&&B.target===g&&S&&(S.unobserve(l),cancelAnimationFrame(C),C=requestAnimationFrame(()=>{var L;(L=S)==null||L.observe(l)})),s()}),g&&!m&&S.observe(g),S.observe(l));let R,E=m?Hn(r):null;m&&I();function I(){const j=Hn(r);E&&!Yd(E,j)&&s(),E=j,R=requestAnimationFrame(I)}return s(),()=>{var j;y.forEach(B=>{c&&B.removeEventListener("scroll",s),f&&B.removeEventListener("resize",s)}),A==null||A(),(j=S)==null||j.disconnect(),S=null,m&&cancelAnimationFrame(R)}}const Fv=sv,_v=av,zv=ov,Uv=cv,Qv=lv,id=rv,Vv=uv,Wv=(r,l,s)=>{const u=new Map,c={platform:jv,...s},f={...c.platform,_c:u};return nv(r,l,{...c,platform:f})};var Hv=typeof document<"u",Yv=function(){},Vl=Hv?w.useLayoutEffect:Yv;function Xl(r,l){if(r===l)return!0;if(typeof r!=typeof l)return!1;if(typeof r=="function"&&r.toString()===l.toString())return!0;let s,u,c;if(r&&l&&typeof r=="object"){if(Array.isArray(r)){if(s=r.length,s!==l.length)return!1;for(u=s;u--!==0;)if(!Xl(r[u],l[u]))return!1;return!0}if(c=Object.keys(r),s=c.length,s!==Object.keys(l).length)return!1;for(u=s;u--!==0;)if(!{}.hasOwnProperty.call(l,c[u]))return!1;for(u=s;u--!==0;){const f=c[u];if(!(f==="_owner"&&r.$$typeof)&&!Xl(r[f],l[f]))return!1}return!0}return r!==r&&l!==l}function Kd(r){return typeof window>"u"?1:(r.ownerDocument.defaultView||window).devicePixelRatio||1}function sd(r,l){const s=Kd(r);return Math.round(l*s)/s}function oa(r){const l=w.useRef(r);return Vl(()=>{l.current=r}),l}function Kv(r){r===void 0&&(r={});const{placement:l="bottom",strategy:s="absolute",middleware:u=[],platform:c,elements:{reference:f,floating:d}={},transform:h=!0,whileElementsMounted:m,open:g}=r,[y,A]=w.useState({x:0,y:0,strategy:s,placement:l,middlewareData:{},isPositioned:!1}),[C,S]=w.useState(u);Xl(C,u)||S(u);const[R,E]=w.useState(null),[I,j]=w.useState(null),B=w.useCallback(_=>{_!==z.current&&(z.current=_,E(_))},[]),L=w.useCallback(_=>{_!==U.current&&(U.current=_,j(_))},[]),F=f||R,M=d||I,z=w.useRef(null),U=w.useRef(null),J=w.useRef(y),X=m!=null,oe=oa(m),me=oa(c),xe=oa(g),ye=w.useCallback(()=>{if(!z.current||!U.current)return;const _={placement:l,strategy:s,middleware:C};me.current&&(_.platform=me.current),Wv(z.current,U.current,_).then(b=>{const G={...b,isPositioned:xe.current!==!1};Y.current&&!Xl(J.current,G)&&(J.current=G,ka.flushSync(()=>{A(G)}))})},[C,l,s,me,xe]);Vl(()=>{g===!1&&J.current.isPositioned&&(J.current.isPositioned=!1,A(_=>({..._,isPositioned:!1})))},[g]);const Y=w.useRef(!1);Vl(()=>(Y.current=!0,()=>{Y.current=!1}),[]),Vl(()=>{if(F&&(z.current=F),M&&(U.current=M),F&&M){if(oe.current)return oe.current(F,M,ye);ye()}},[F,M,ye,oe,X]);const Z=w.useMemo(()=>({reference:z,floating:U,setReference:B,setFloating:L}),[B,L]),le=w.useMemo(()=>({reference:F,floating:M}),[F,M]),te=w.useMemo(()=>{const _={position:s,left:0,top:0};if(!le.floating)return _;const b=sd(le.floating,y.x),G=sd(le.floating,y.y);return h?{..._,transform:"translate("+b+"px, "+G+"px)",...Kd(le.floating)>=1.5&&{willChange:"transform"}}:{position:s,left:b,top:G}},[s,h,le.floating,y.x,y.y]);return w.useMemo(()=>({...y,update:ye,refs:Z,elements:le,floatingStyles:te}),[y,ye,Z,le,te])}const Gv=r=>{function l(s){return{}.hasOwnProperty.call(s,"current")}return{name:"arrow",options:r,fn(s){const{element:u,padding:c}=typeof r=="function"?r(s):r;return u&&l(u)?u.current!=null?id({element:u.current,padding:c}).fn(s):{}:u?id({element:u,padding:c}).fn(s):{}}}},bv=(r,l)=>({...Fv(r),options:[r,l]}),Jv=(r,l)=>({..._v(r),options:[r,l]}),Xv=(r,l)=>({...Vv(r),options:[r,l]}),$v=(r,l)=>({...zv(r),options:[r,l]}),Zv=(r,l)=>({...Uv(r),options:[r,l]}),qv=(r,l)=>({...Qv(r),options:[r,l]}),ey=(r,l)=>({...Gv(r),options:[r,l]});var ty="Arrow",Gd=w.forwardRef((r,l)=>{const{children:s,width:u=10,height:c=5,...f}=r;return v.jsx(xt.svg,{...f,ref:l,width:u,height:c,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:r.asChild?s:v.jsx("polygon",{points:"0,0 30,0 15,10"})})});Gd.displayName=ty;var ny=Gd;function ry(r){const[l,s]=w.useState(void 0);return Nn(()=>{if(r){s({width:r.offsetWidth,height:r.offsetHeight});const u=new ResizeObserver(c=>{if(!Array.isArray(c)||!c.length)return;const f=c[0];let d,h;if("borderBoxSize"in f){const m=f.borderBoxSize,g=Array.isArray(m)?m[0]:m;d=g.inlineSize,h=g.blockSize}else d=r.offsetWidth,h=r.offsetHeight;s({width:d,height:h})});return u.observe(r,{box:"border-box"}),()=>u.unobserve(r)}else s(void 0)},[r]),l}var La="Popper",[bd,ti]=Na(La),[oy,Jd]=bd(La),Xd=r=>{const{__scopePopper:l,children:s}=r,[u,c]=w.useState(null);return v.jsx(oy,{scope:l,anchor:u,onAnchorChange:c,children:s})};Xd.displayName=La;var $d="PopperAnchor",Zd=w.forwardRef((r,l)=>{const{__scopePopper:s,virtualRef:u,...c}=r,f=Jd($d,s),d=w.useRef(null),h=Ht(l,d);return w.useEffect(()=>{f.onAnchorChange((u==null?void 0:u.current)||d.current)}),u?null:v.jsx(xt.div,{...c,ref:h})});Zd.displayName=$d;var Ba="PopperContent",[ly,iy]=bd(Ba),qd=w.forwardRef((r,l)=>{var ve,de,we,Ee,Ke,ie;const{__scopePopper:s,side:u="bottom",sideOffset:c=0,align:f="center",alignOffset:d=0,arrowPadding:h=0,avoidCollisions:m=!0,collisionBoundary:g=[],collisionPadding:y=0,sticky:A="partial",hideWhenDetached:C=!1,updatePositionStrategy:S="optimized",onPlaced:R,...E}=r,I=Jd(Ba,s),[j,B]=w.useState(null),L=Ht(l,ge=>B(ge)),[F,M]=w.useState(null),z=ry(F),U=(z==null?void 0:z.width)??0,J=(z==null?void 0:z.height)??0,X=u+(f!=="center"?"-"+f:""),oe=typeof y=="number"?y:{top:0,right:0,bottom:0,left:0,...y},me=Array.isArray(g)?g:[g],xe=me.length>0,ye={padding:oe,boundary:me.filter(ay),altBoundary:xe},{refs:Y,floatingStyles:Z,placement:le,isPositioned:te,middlewareData:_}=Kv({strategy:"fixed",placement:X,whileElementsMounted:(...ge)=>Bv(...ge,{animationFrame:S==="always"}),elements:{reference:I.anchor},middleware:[bv({mainAxis:c+J,alignmentAxis:d}),m&&Jv({mainAxis:!0,crossAxis:!1,limiter:A==="partial"?Xv():void 0,...ye}),m&&$v({...ye}),Zv({...ye,apply:({elements:ge,rects:Me,availableWidth:Ge,availableHeight:Mt})=>{const{width:rn,height:Yn}=Me.reference,pt=ge.floating.style;pt.setProperty("--radix-popper-available-width",`${Ge}px`),pt.setProperty("--radix-popper-available-height",`${Mt}px`),pt.setProperty("--radix-popper-anchor-width",`${rn}px`),pt.setProperty("--radix-popper-anchor-height",`${Yn}px`)}}),F&&ey({element:F,padding:h}),uy({arrowWidth:U,arrowHeight:J}),C&&qv({strategy:"referenceHidden",...ye})]}),[b,G]=np(le),P=kr(R);Nn(()=>{te&&(P==null||P())},[te,P]);const Q=(ve=_.arrow)==null?void 0:ve.x,ue=(de=_.arrow)==null?void 0:de.y,ce=((we=_.arrow)==null?void 0:we.centerOffset)!==0,[he,fe]=w.useState();return Nn(()=>{j&&fe(window.getComputedStyle(j).zIndex)},[j]),v.jsx("div",{ref:Y.setFloating,"data-radix-popper-content-wrapper":"",style:{...Z,transform:te?Z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:he,"--radix-popper-transform-origin":[(Ee=_.transformOrigin)==null?void 0:Ee.x,(Ke=_.transformOrigin)==null?void 0:Ke.y].join(" "),...((ie=_.hide)==null?void 0:ie.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:r.dir,children:v.jsx(ly,{scope:s,placedSide:b,onArrowChange:M,arrowX:Q,arrowY:ue,shouldHideArrow:ce,children:v.jsx(xt.div,{"data-side":b,"data-align":G,...E,ref:L,style:{...E.style,animation:te?void 0:"none"}})})})});qd.displayName=Ba;var ep="PopperArrow",sy={top:"bottom",right:"left",bottom:"top",left:"right"},tp=w.forwardRef(function(l,s){const{__scopePopper:u,...c}=l,f=iy(ep,u),d=sy[f.placedSide];return v.jsx("span",{ref:f.onArrowChange,style:{position:"absolute",left:f.arrowX,top:f.arrowY,[d]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f.placedSide],visibility:f.shouldHideArrow?"hidden":void 0},children:v.jsx(ny,{...c,ref:s,style:{...c.style,display:"block"}})})});tp.displayName=ep;function ay(r){return r!==null}var uy=r=>({name:"transformOrigin",options:r,fn(l){var I,j,B;const{placement:s,rects:u,middlewareData:c}=l,d=((I=c.arrow)==null?void 0:I.centerOffset)!==0,h=d?0:r.arrowWidth,m=d?0:r.arrowHeight,[g,y]=np(s),A={start:"0%",center:"50%",end:"100%"}[y],C=(((j=c.arrow)==null?void 0:j.x)??0)+h/2,S=(((B=c.arrow)==null?void 0:B.y)??0)+m/2;let R="",E="";return g==="bottom"?(R=d?A:`${C}px`,E=`${-m}px`):g==="top"?(R=d?A:`${C}px`,E=`${u.floating.height+m}px`):g==="right"?(R=`${-m}px`,E=d?A:`${S}px`):g==="left"&&(R=`${u.floating.width+m}px`,E=d?A:`${S}px`),{data:{x:R,y:E}}}});function np(r){const[l,s="center"]=r.split("-");return[l,s]}var rp=Xd,Fa=Zd,op=qd,lp=tp,cy="Portal",_a=w.forwardRef((r,l)=>{var h;const{container:s,...u}=r,[c,f]=w.useState(!1);Nn(()=>f(!0),[]);const d=s||c&&((h=globalThis==null?void 0:globalThis.document)==null?void 0:h.body);return d?wg.createPortal(v.jsx(xt.div,{...u,ref:l}),d):null});_a.displayName=cy;function fy(r,l){return w.useReducer((s,u)=>l[s][u]??s,r)}var Co=r=>{const{present:l,children:s}=r,u=dy(l),c=typeof s=="function"?s({present:u.isPresent}):w.Children.only(s),f=Ht(u.ref,py(c));return typeof s=="function"||u.isPresent?w.cloneElement(c,{ref:f}):null};Co.displayName="Presence";function dy(r){const[l,s]=w.useState(),u=w.useRef(null),c=w.useRef(r),f=w.useRef("none"),d=r?"mounted":"unmounted",[h,m]=fy(d,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const g=Bl(u.current);f.current=h==="mounted"?g:"none"},[h]),Nn(()=>{const g=u.current,y=c.current;if(y!==r){const C=f.current,S=Bl(g);r?m("MOUNT"):S==="none"||(g==null?void 0:g.display)==="none"?m("UNMOUNT"):m(y&&C!==S?"ANIMATION_OUT":"UNMOUNT"),c.current=r}},[r,m]),Nn(()=>{if(l){let g;const y=l.ownerDocument.defaultView??window,A=S=>{const E=Bl(u.current).includes(S.animationName);if(S.target===l&&E&&(m("ANIMATION_END"),!c.current)){const I=l.style.animationFillMode;l.style.animationFillMode="forwards",g=y.setTimeout(()=>{l.style.animationFillMode==="forwards"&&(l.style.animationFillMode=I)})}},C=S=>{S.target===l&&(f.current=Bl(u.current))};return l.addEventListener("animationstart",C),l.addEventListener("animationcancel",A),l.addEventListener("animationend",A),()=>{y.clearTimeout(g),l.removeEventListener("animationstart",C),l.removeEventListener("animationcancel",A),l.removeEventListener("animationend",A)}}else m("ANIMATION_END")},[l,m]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:w.useCallback(g=>{u.current=g?getComputedStyle(g):null,s(g)},[])}}function Bl(r){return(r==null?void 0:r.animationName)||"none"}function py(r){var u,c;let l=(u=Object.getOwnPropertyDescriptor(r.props,"ref"))==null?void 0:u.get,s=l&&"isReactWarning"in l&&l.isReactWarning;return s?r.ref:(l=(c=Object.getOwnPropertyDescriptor(r,"ref"))==null?void 0:c.get,s=l&&"isReactWarning"in l&&l.isReactWarning,s?r.props.ref:r.props.ref||r.ref)}var hy=xd[" useInsertionEffect ".trim().toString()]||Nn;function ip({prop:r,defaultProp:l,onChange:s=()=>{},caller:u}){const[c,f,d]=my({defaultProp:l,onChange:s}),h=r!==void 0,m=h?r:c;{const y=w.useRef(r!==void 0);w.useEffect(()=>{const A=y.current;A!==h&&console.warn(`${u} is changing from ${A?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),y.current=h},[h,u])}const g=w.useCallback(y=>{var A;if(h){const C=gy(y)?y(r):y;C!==r&&((A=d.current)==null||A.call(d,C))}else f(y)},[h,r,f,d]);return[m,g]}function my({defaultProp:r,onChange:l}){const[s,u]=w.useState(r),c=w.useRef(s),f=w.useRef(l);return hy(()=>{f.current=l},[l]),w.useEffect(()=>{var d;c.current!==s&&((d=f.current)==null||d.call(f,s),c.current=s)},[s,c]),[s,u,f]}function gy(r){return typeof r=="function"}var vy=function(r){if(typeof document>"u")return null;var l=Array.isArray(r)?r[0]:r;return l.ownerDocument.body},gr=new WeakMap,Fl=new WeakMap,_l={},la=0,sp=function(r){return r&&(r.host||sp(r.parentNode))},yy=function(r,l){return l.map(function(s){if(r.contains(s))return s;var u=sp(s);return u&&r.contains(u)?u:(console.error("aria-hidden",s,"in not contained inside",r,". Doing nothing"),null)}).filter(function(s){return!!s})},Ay=function(r,l,s,u){var c=yy(l,Array.isArray(r)?r:[r]);_l[s]||(_l[s]=new WeakMap);var f=_l[s],d=[],h=new Set,m=new Set(c),g=function(A){!A||h.has(A)||(h.add(A),g(A.parentNode))};c.forEach(g);var y=function(A){!A||m.has(A)||Array.prototype.forEach.call(A.children,function(C){if(h.has(C))y(C);else try{var S=C.getAttribute(u),R=S!==null&&S!=="false",E=(gr.get(C)||0)+1,I=(f.get(C)||0)+1;gr.set(C,E),f.set(C,I),d.push(C),E===1&&R&&Fl.set(C,!0),I===1&&C.setAttribute(s,"true"),R||C.setAttribute(u,"true")}catch(j){console.error("aria-hidden: cannot operate on ",C,j)}})};return y(l),h.clear(),la++,function(){d.forEach(function(A){var C=gr.get(A)-1,S=f.get(A)-1;gr.set(A,C),f.set(A,S),C||(Fl.has(A)||A.removeAttribute(u),Fl.delete(A)),S||A.removeAttribute(s)}),la--,la||(gr=new WeakMap,gr=new WeakMap,Fl=new WeakMap,_l={})}},wy=function(r,l,s){s===void 0&&(s="data-aria-hidden");var u=Array.from(Array.isArray(r)?r:[r]),c=vy(r);return c?(u.push.apply(u,Array.from(c.querySelectorAll("[aria-live], script"))),Ay(u,c,s,"aria-hidden")):function(){return null}},zt=function(){return zt=Object.assign||function(l){for(var s,u=1,c=arguments.length;u<c;u++){s=arguments[u];for(var f in s)Object.prototype.hasOwnProperty.call(s,f)&&(l[f]=s[f])}return l},zt.apply(this,arguments)};function ap(r,l){var s={};for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&l.indexOf(u)<0&&(s[u]=r[u]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,u=Object.getOwnPropertySymbols(r);c<u.length;c++)l.indexOf(u[c])<0&&Object.prototype.propertyIsEnumerable.call(r,u[c])&&(s[u[c]]=r[u[c]]);return s}function xy(r,l,s){if(s||arguments.length===2)for(var u=0,c=l.length,f;u<c;u++)(f||!(u in l))&&(f||(f=Array.prototype.slice.call(l,0,u)),f[u]=l[u]);return r.concat(f||Array.prototype.slice.call(l))}var Wl="right-scroll-bar-position",Hl="width-before-scroll-bar",Cy="with-scroll-bars-hidden",Sy="--removed-body-scroll-bar-size";function ia(r,l){return typeof r=="function"?r(l):r&&(r.current=l),r}function Ey(r,l){var s=w.useState(function(){return{value:r,callback:l,facade:{get current(){return s.value},set current(u){var c=s.value;c!==u&&(s.value=u,s.callback(u,c))}}}})[0];return s.callback=l,s.facade}var ky=typeof window<"u"?w.useLayoutEffect:w.useEffect,ad=new WeakMap;function Ny(r,l){var s=Ey(null,function(u){return r.forEach(function(c){return ia(c,u)})});return ky(function(){var u=ad.get(s);if(u){var c=new Set(u),f=new Set(r),d=s.current;c.forEach(function(h){f.has(h)||ia(h,null)}),f.forEach(function(h){c.has(h)||ia(h,d)})}ad.set(s,r)},[r]),s}function Py(r){return r}function Ry(r,l){l===void 0&&(l=Py);var s=[],u=!1,c={read:function(){if(u)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:r},useMedium:function(f){var d=l(f,u);return s.push(d),function(){s=s.filter(function(h){return h!==d})}},assignSyncMedium:function(f){for(u=!0;s.length;){var d=s;s=[],d.forEach(f)}s={push:function(h){return f(h)},filter:function(){return s}}},assignMedium:function(f){u=!0;var d=[];if(s.length){var h=s;s=[],h.forEach(f),d=s}var m=function(){var y=d;d=[],y.forEach(f)},g=function(){return Promise.resolve().then(m)};g(),s={push:function(y){d.push(y),g()},filter:function(y){return d=d.filter(y),s}}}};return c}function Iy(r){r===void 0&&(r={});var l=Ry(null);return l.options=zt({async:!0,ssr:!1},r),l}var up=function(r){var l=r.sideCar,s=ap(r,["sideCar"]);if(!l)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var u=l.read();if(!u)throw new Error("Sidecar medium not found");return w.createElement(u,zt({},s))};up.isSideCarExport=!0;function Dy(r,l){return r.useMedium(l),up}var cp=Iy(),sa=function(){},ni=w.forwardRef(function(r,l){var s=w.useRef(null),u=w.useState({onScrollCapture:sa,onWheelCapture:sa,onTouchMoveCapture:sa}),c=u[0],f=u[1],d=r.forwardProps,h=r.children,m=r.className,g=r.removeScrollBar,y=r.enabled,A=r.shards,C=r.sideCar,S=r.noRelative,R=r.noIsolation,E=r.inert,I=r.allowPinchZoom,j=r.as,B=j===void 0?"div":j,L=r.gapMode,F=ap(r,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=C,z=Ny([s,l]),U=zt(zt({},F),c);return w.createElement(w.Fragment,null,y&&w.createElement(M,{sideCar:cp,removeScrollBar:g,shards:A,noRelative:S,noIsolation:R,inert:E,setCallbacks:f,allowPinchZoom:!!I,lockRef:s,gapMode:L}),d?w.cloneElement(w.Children.only(h),zt(zt({},U),{ref:z})):w.createElement(B,zt({},U,{className:m,ref:z}),h))});ni.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ni.classNames={fullWidth:Hl,zeroRight:Wl};var Oy=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Ty(){if(!document)return null;var r=document.createElement("style");r.type="text/css";var l=Oy();return l&&r.setAttribute("nonce",l),r}function My(r,l){r.styleSheet?r.styleSheet.cssText=l:r.appendChild(document.createTextNode(l))}function jy(r){var l=document.head||document.getElementsByTagName("head")[0];l.appendChild(r)}var Ly=function(){var r=0,l=null;return{add:function(s){r==0&&(l=Ty())&&(My(l,s),jy(l)),r++},remove:function(){r--,!r&&l&&(l.parentNode&&l.parentNode.removeChild(l),l=null)}}},By=function(){var r=Ly();return function(l,s){w.useEffect(function(){return r.add(l),function(){r.remove()}},[l&&s])}},fp=function(){var r=By(),l=function(s){var u=s.styles,c=s.dynamic;return r(u,c),null};return l},Fy={left:0,top:0,right:0,gap:0},aa=function(r){return parseInt(r||"",10)||0},_y=function(r){var l=window.getComputedStyle(document.body),s=l[r==="padding"?"paddingLeft":"marginLeft"],u=l[r==="padding"?"paddingTop":"marginTop"],c=l[r==="padding"?"paddingRight":"marginRight"];return[aa(s),aa(u),aa(c)]},zy=function(r){if(r===void 0&&(r="margin"),typeof window>"u")return Fy;var l=_y(r),s=document.documentElement.clientWidth,u=window.innerWidth;return{left:l[0],top:l[1],right:l[2],gap:Math.max(0,u-s+l[2]-l[0])}},Uy=fp(),Er="data-scroll-locked",Qy=function(r,l,s,u){var c=r.left,f=r.top,d=r.right,h=r.gap;return s===void 0&&(s="margin"),`
  .`.concat(Cy,` {
   overflow: hidden `).concat(u,`;
   padding-right: `).concat(h,"px ").concat(u,`;
  }
  body[`).concat(Er,`] {
    overflow: hidden `).concat(u,`;
    overscroll-behavior: contain;
    `).concat([l&&"position: relative ".concat(u,";"),s==="margin"&&`
    padding-left: `.concat(c,`px;
    padding-top: `).concat(f,`px;
    padding-right: `).concat(d,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(u,`;
    `),s==="padding"&&"padding-right: ".concat(h,"px ").concat(u,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Wl,` {
    right: `).concat(h,"px ").concat(u,`;
  }
  
  .`).concat(Hl,` {
    margin-right: `).concat(h,"px ").concat(u,`;
  }
  
  .`).concat(Wl," .").concat(Wl,` {
    right: 0 `).concat(u,`;
  }
  
  .`).concat(Hl," .").concat(Hl,` {
    margin-right: 0 `).concat(u,`;
  }
  
  body[`).concat(Er,`] {
    `).concat(Sy,": ").concat(h,`px;
  }
`)},ud=function(){var r=parseInt(document.body.getAttribute(Er)||"0",10);return isFinite(r)?r:0},Vy=function(){w.useEffect(function(){return document.body.setAttribute(Er,(ud()+1).toString()),function(){var r=ud()-1;r<=0?document.body.removeAttribute(Er):document.body.setAttribute(Er,r.toString())}},[])},Wy=function(r){var l=r.noRelative,s=r.noImportant,u=r.gapMode,c=u===void 0?"margin":u;Vy();var f=w.useMemo(function(){return zy(c)},[c]);return w.createElement(Uy,{styles:Qy(f,!l,c,s?"":"!important")})},ma=!1;if(typeof window<"u")try{var zl=Object.defineProperty({},"passive",{get:function(){return ma=!0,!0}});window.addEventListener("test",zl,zl),window.removeEventListener("test",zl,zl)}catch{ma=!1}var vr=ma?{passive:!1}:!1,Hy=function(r){return r.tagName==="TEXTAREA"},dp=function(r,l){if(!(r instanceof Element))return!1;var s=window.getComputedStyle(r);return s[l]!=="hidden"&&!(s.overflowY===s.overflowX&&!Hy(r)&&s[l]==="visible")},Yy=function(r){return dp(r,"overflowY")},Ky=function(r){return dp(r,"overflowX")},cd=function(r,l){var s=l.ownerDocument,u=l;do{typeof ShadowRoot<"u"&&u instanceof ShadowRoot&&(u=u.host);var c=pp(r,u);if(c){var f=hp(r,u),d=f[1],h=f[2];if(d>h)return!0}u=u.parentNode}while(u&&u!==s.body);return!1},Gy=function(r){var l=r.scrollTop,s=r.scrollHeight,u=r.clientHeight;return[l,s,u]},by=function(r){var l=r.scrollLeft,s=r.scrollWidth,u=r.clientWidth;return[l,s,u]},pp=function(r,l){return r==="v"?Yy(l):Ky(l)},hp=function(r,l){return r==="v"?Gy(l):by(l)},Jy=function(r,l){return r==="h"&&l==="rtl"?-1:1},Xy=function(r,l,s,u,c){var f=Jy(r,window.getComputedStyle(l).direction),d=f*u,h=s.target,m=l.contains(h),g=!1,y=d>0,A=0,C=0;do{if(!h)break;var S=hp(r,h),R=S[0],E=S[1],I=S[2],j=E-I-f*R;(R||j)&&pp(r,h)&&(A+=j,C+=R);var B=h.parentNode;h=B&&B.nodeType===Node.DOCUMENT_FRAGMENT_NODE?B.host:B}while(!m&&h!==document.body||m&&(l.contains(h)||l===h));return(y&&Math.abs(A)<1||!y&&Math.abs(C)<1)&&(g=!0),g},Ul=function(r){return"changedTouches"in r?[r.changedTouches[0].clientX,r.changedTouches[0].clientY]:[0,0]},fd=function(r){return[r.deltaX,r.deltaY]},dd=function(r){return r&&"current"in r?r.current:r},$y=function(r,l){return r[0]===l[0]&&r[1]===l[1]},Zy=function(r){return`
  .block-interactivity-`.concat(r,` {pointer-events: none;}
  .allow-interactivity-`).concat(r,` {pointer-events: all;}
`)},qy=0,yr=[];function e0(r){var l=w.useRef([]),s=w.useRef([0,0]),u=w.useRef(),c=w.useState(qy++)[0],f=w.useState(fp)[0],d=w.useRef(r);w.useEffect(function(){d.current=r},[r]),w.useEffect(function(){if(r.inert){document.body.classList.add("block-interactivity-".concat(c));var E=xy([r.lockRef.current],(r.shards||[]).map(dd),!0).filter(Boolean);return E.forEach(function(I){return I.classList.add("allow-interactivity-".concat(c))}),function(){document.body.classList.remove("block-interactivity-".concat(c)),E.forEach(function(I){return I.classList.remove("allow-interactivity-".concat(c))})}}},[r.inert,r.lockRef.current,r.shards]);var h=w.useCallback(function(E,I){if("touches"in E&&E.touches.length===2||E.type==="wheel"&&E.ctrlKey)return!d.current.allowPinchZoom;var j=Ul(E),B=s.current,L="deltaX"in E?E.deltaX:B[0]-j[0],F="deltaY"in E?E.deltaY:B[1]-j[1],M,z=E.target,U=Math.abs(L)>Math.abs(F)?"h":"v";if("touches"in E&&U==="h"&&z.type==="range")return!1;var J=cd(U,z);if(!J)return!0;if(J?M=U:(M=U==="v"?"h":"v",J=cd(U,z)),!J)return!1;if(!u.current&&"changedTouches"in E&&(L||F)&&(u.current=M),!M)return!0;var X=u.current||M;return Xy(X,I,E,X==="h"?L:F)},[]),m=w.useCallback(function(E){var I=E;if(!(!yr.length||yr[yr.length-1]!==f)){var j="deltaY"in I?fd(I):Ul(I),B=l.current.filter(function(M){return M.name===I.type&&(M.target===I.target||I.target===M.shadowParent)&&$y(M.delta,j)})[0];if(B&&B.should){I.cancelable&&I.preventDefault();return}if(!B){var L=(d.current.shards||[]).map(dd).filter(Boolean).filter(function(M){return M.contains(I.target)}),F=L.length>0?h(I,L[0]):!d.current.noIsolation;F&&I.cancelable&&I.preventDefault()}}},[]),g=w.useCallback(function(E,I,j,B){var L={name:E,delta:I,target:j,should:B,shadowParent:t0(j)};l.current.push(L),setTimeout(function(){l.current=l.current.filter(function(F){return F!==L})},1)},[]),y=w.useCallback(function(E){s.current=Ul(E),u.current=void 0},[]),A=w.useCallback(function(E){g(E.type,fd(E),E.target,h(E,r.lockRef.current))},[]),C=w.useCallback(function(E){g(E.type,Ul(E),E.target,h(E,r.lockRef.current))},[]);w.useEffect(function(){return yr.push(f),r.setCallbacks({onScrollCapture:A,onWheelCapture:A,onTouchMoveCapture:C}),document.addEventListener("wheel",m,vr),document.addEventListener("touchmove",m,vr),document.addEventListener("touchstart",y,vr),function(){yr=yr.filter(function(E){return E!==f}),document.removeEventListener("wheel",m,vr),document.removeEventListener("touchmove",m,vr),document.removeEventListener("touchstart",y,vr)}},[]);var S=r.removeScrollBar,R=r.inert;return w.createElement(w.Fragment,null,R?w.createElement(f,{styles:Zy(c)}):null,S?w.createElement(Wy,{noRelative:r.noRelative,gapMode:r.gapMode}):null)}function t0(r){for(var l=null;r!==null;)r instanceof ShadowRoot&&(l=r.host,r=r.host),r=r.parentNode;return l}const n0=Dy(cp,e0);var mp=w.forwardRef(function(r,l){return w.createElement(ni,zt({},r,{ref:l,sideCar:n0}))});mp.classNames=ni.classNames;var ri="Popover",[gp,jA]=Na(ri,[ti]),So=ti(),[r0,In]=gp(ri),vp=r=>{const{__scopePopover:l,children:s,open:u,defaultOpen:c,onOpenChange:f,modal:d=!1}=r,h=So(l),m=w.useRef(null),[g,y]=w.useState(!1),[A,C]=ip({prop:u,defaultProp:c??!1,onChange:f,caller:ri});return v.jsx(rp,{...h,children:v.jsx(r0,{scope:l,contentId:Ld(),triggerRef:m,open:A,onOpenChange:C,onOpenToggle:w.useCallback(()=>C(S=>!S),[C]),hasCustomAnchor:g,onCustomAnchorAdd:w.useCallback(()=>y(!0),[]),onCustomAnchorRemove:w.useCallback(()=>y(!1),[]),modal:d,children:s})})};vp.displayName=ri;var yp="PopoverAnchor",o0=w.forwardRef((r,l)=>{const{__scopePopover:s,...u}=r,c=In(yp,s),f=So(s),{onCustomAnchorAdd:d,onCustomAnchorRemove:h}=c;return w.useEffect(()=>(d(),()=>h()),[d,h]),v.jsx(Fa,{...f,...u,ref:l})});o0.displayName=yp;var Ap="PopoverTrigger",wp=w.forwardRef((r,l)=>{const{__scopePopover:s,...u}=r,c=In(Ap,s),f=So(s),d=Ht(l,c.triggerRef),h=v.jsx(xt.button,{type:"button","aria-haspopup":"dialog","aria-expanded":c.open,"aria-controls":c.contentId,"data-state":kp(c.open),...u,ref:d,onClick:it(r.onClick,c.onOpenToggle)});return c.hasCustomAnchor?h:v.jsx(Fa,{asChild:!0,...f,children:h})});wp.displayName=Ap;var za="PopoverPortal",[l0,i0]=gp(za,{forceMount:void 0}),xp=r=>{const{__scopePopover:l,forceMount:s,children:u,container:c}=r,f=In(za,l);return v.jsx(l0,{scope:l,forceMount:s,children:v.jsx(Co,{present:s||f.open,children:v.jsx(_a,{asChild:!0,container:c,children:u})})})};xp.displayName=za;var Pr="PopoverContent",Cp=w.forwardRef((r,l)=>{const s=i0(Pr,r.__scopePopover),{forceMount:u=s.forceMount,...c}=r,f=In(Pr,r.__scopePopover);return v.jsx(Co,{present:u||f.open,children:f.modal?v.jsx(a0,{...c,ref:l}):v.jsx(u0,{...c,ref:l})})});Cp.displayName=Pr;var s0=Ea("PopoverContent.RemoveScroll"),a0=w.forwardRef((r,l)=>{const s=In(Pr,r.__scopePopover),u=w.useRef(null),c=Ht(l,u),f=w.useRef(!1);return w.useEffect(()=>{const d=u.current;if(d)return wy(d)},[]),v.jsx(mp,{as:s0,allowPinchZoom:!0,children:v.jsx(Sp,{...r,ref:c,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:it(r.onCloseAutoFocus,d=>{var h;d.preventDefault(),f.current||(h=s.triggerRef.current)==null||h.focus()}),onPointerDownOutside:it(r.onPointerDownOutside,d=>{const h=d.detail.originalEvent,m=h.button===0&&h.ctrlKey===!0,g=h.button===2||m;f.current=g},{checkForDefaultPrevented:!1}),onFocusOutside:it(r.onFocusOutside,d=>d.preventDefault(),{checkForDefaultPrevented:!1})})})}),u0=w.forwardRef((r,l)=>{const s=In(Pr,r.__scopePopover),u=w.useRef(!1),c=w.useRef(!1);return v.jsx(Sp,{...r,ref:l,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:f=>{var d,h;(d=r.onCloseAutoFocus)==null||d.call(r,f),f.defaultPrevented||(u.current||(h=s.triggerRef.current)==null||h.focus(),f.preventDefault()),u.current=!1,c.current=!1},onInteractOutside:f=>{var m,g;(m=r.onInteractOutside)==null||m.call(r,f),f.defaultPrevented||(u.current=!0,f.detail.originalEvent.type==="pointerdown"&&(c.current=!0));const d=f.target;((g=s.triggerRef.current)==null?void 0:g.contains(d))&&f.preventDefault(),f.detail.originalEvent.type==="focusin"&&c.current&&f.preventDefault()}})}),Sp=w.forwardRef((r,l)=>{const{__scopePopover:s,trapFocus:u,onOpenAutoFocus:c,onCloseAutoFocus:f,disableOutsidePointerEvents:d,onEscapeKeyDown:h,onPointerDownOutside:m,onFocusOutside:g,onInteractOutside:y,...A}=r,C=In(Pr,s),S=So(s);return Lg(),v.jsx(Md,{asChild:!0,loop:!0,trapped:u,onMountAutoFocus:c,onUnmountAutoFocus:f,children:v.jsx(Pa,{asChild:!0,disableOutsidePointerEvents:d,onInteractOutside:y,onEscapeKeyDown:h,onPointerDownOutside:m,onFocusOutside:g,onDismiss:()=>C.onOpenChange(!1),children:v.jsx(op,{"data-state":kp(C.open),role:"dialog",id:C.contentId,...S,...A,ref:l,style:{...A.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),Ep="PopoverClose",c0=w.forwardRef((r,l)=>{const{__scopePopover:s,...u}=r,c=In(Ep,s);return v.jsx(xt.button,{type:"button",...u,ref:l,onClick:it(r.onClick,()=>c.onOpenChange(!1))})});c0.displayName=Ep;var f0="PopoverArrow",d0=w.forwardRef((r,l)=>{const{__scopePopover:s,...u}=r,c=So(s);return v.jsx(lp,{...c,...u,ref:l})});d0.displayName=f0;function kp(r){return r?"open":"closed"}var p0=vp,h0=wp,m0=xp,Np=Cp;const ga=p0,va=h0,$l=w.forwardRef(({className:r="",align:l="center",sideOffset:s=4,...u},c)=>{const f=()=>{let d="popover-content";return r&&(d+=` ${r}`),d};return v.jsx(m0,{children:v.jsx(Np,{ref:c,align:l,sideOffset:s,className:f(),...u})})});$l.displayName=Np.displayName;var g0=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),v0="VisuallyHidden",Pp=w.forwardRef((r,l)=>v.jsx(xt.span,{...r,ref:l,style:{...g0,...r.style}}));Pp.displayName=v0;var y0=Pp,[oi,LA]=Na("Tooltip",[ti]),li=ti(),Rp="TooltipProvider",A0=700,ya="tooltip.open",[w0,Ua]=oi(Rp),Ip=r=>{const{__scopeTooltip:l,delayDuration:s=A0,skipDelayDuration:u=300,disableHoverableContent:c=!1,children:f}=r,d=w.useRef(!0),h=w.useRef(!1),m=w.useRef(0);return w.useEffect(()=>{const g=m.current;return()=>window.clearTimeout(g)},[]),v.jsx(w0,{scope:l,isOpenDelayedRef:d,delayDuration:s,onOpen:w.useCallback(()=>{window.clearTimeout(m.current),d.current=!1},[]),onClose:w.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>d.current=!0,u)},[u]),isPointerInTransitRef:h,onPointerInTransitChange:w.useCallback(g=>{h.current=g},[]),disableHoverableContent:c,children:f})};Ip.displayName=Rp;var wo="Tooltip",[x0,Eo]=oi(wo),Dp=r=>{const{__scopeTooltip:l,children:s,open:u,defaultOpen:c,onOpenChange:f,disableHoverableContent:d,delayDuration:h}=r,m=Ua(wo,r.__scopeTooltip),g=li(l),[y,A]=w.useState(null),C=Ld(),S=w.useRef(0),R=d??m.disableHoverableContent,E=h??m.delayDuration,I=w.useRef(!1),[j,B]=ip({prop:u,defaultProp:c??!1,onChange:U=>{U?(m.onOpen(),document.dispatchEvent(new CustomEvent(ya))):m.onClose(),f==null||f(U)},caller:wo}),L=w.useMemo(()=>j?I.current?"delayed-open":"instant-open":"closed",[j]),F=w.useCallback(()=>{window.clearTimeout(S.current),S.current=0,I.current=!1,B(!0)},[B]),M=w.useCallback(()=>{window.clearTimeout(S.current),S.current=0,B(!1)},[B]),z=w.useCallback(()=>{window.clearTimeout(S.current),S.current=window.setTimeout(()=>{I.current=!0,B(!0),S.current=0},E)},[E,B]);return w.useEffect(()=>()=>{S.current&&(window.clearTimeout(S.current),S.current=0)},[]),v.jsx(rp,{...g,children:v.jsx(x0,{scope:l,contentId:C,open:j,stateAttribute:L,trigger:y,onTriggerChange:A,onTriggerEnter:w.useCallback(()=>{m.isOpenDelayedRef.current?z():F()},[m.isOpenDelayedRef,z,F]),onTriggerLeave:w.useCallback(()=>{R?M():(window.clearTimeout(S.current),S.current=0)},[M,R]),onOpen:F,onClose:M,disableHoverableContent:R,children:s})})};Dp.displayName=wo;var Aa="TooltipTrigger",Op=w.forwardRef((r,l)=>{const{__scopeTooltip:s,...u}=r,c=Eo(Aa,s),f=Ua(Aa,s),d=li(s),h=w.useRef(null),m=Ht(l,h,c.onTriggerChange),g=w.useRef(!1),y=w.useRef(!1),A=w.useCallback(()=>g.current=!1,[]);return w.useEffect(()=>()=>document.removeEventListener("pointerup",A),[A]),v.jsx(Fa,{asChild:!0,...d,children:v.jsx(xt.button,{"aria-describedby":c.open?c.contentId:void 0,"data-state":c.stateAttribute,...u,ref:m,onPointerMove:it(r.onPointerMove,C=>{C.pointerType!=="touch"&&!y.current&&!f.isPointerInTransitRef.current&&(c.onTriggerEnter(),y.current=!0)}),onPointerLeave:it(r.onPointerLeave,()=>{c.onTriggerLeave(),y.current=!1}),onPointerDown:it(r.onPointerDown,()=>{c.open&&c.onClose(),g.current=!0,document.addEventListener("pointerup",A,{once:!0})}),onFocus:it(r.onFocus,()=>{g.current||c.onOpen()}),onBlur:it(r.onBlur,c.onClose),onClick:it(r.onClick,c.onClose)})})});Op.displayName=Aa;var Qa="TooltipPortal",[C0,S0]=oi(Qa,{forceMount:void 0}),Tp=r=>{const{__scopeTooltip:l,forceMount:s,children:u,container:c}=r,f=Eo(Qa,l);return v.jsx(C0,{scope:l,forceMount:s,children:v.jsx(Co,{present:s||f.open,children:v.jsx(_a,{asChild:!0,container:c,children:u})})})};Tp.displayName=Qa;var Rr="TooltipContent",Mp=w.forwardRef((r,l)=>{const s=S0(Rr,r.__scopeTooltip),{forceMount:u=s.forceMount,side:c="top",...f}=r,d=Eo(Rr,r.__scopeTooltip);return v.jsx(Co,{present:u||d.open,children:d.disableHoverableContent?v.jsx(jp,{side:c,...f,ref:l}):v.jsx(E0,{side:c,...f,ref:l})})}),E0=w.forwardRef((r,l)=>{const s=Eo(Rr,r.__scopeTooltip),u=Ua(Rr,r.__scopeTooltip),c=w.useRef(null),f=Ht(l,c),[d,h]=w.useState(null),{trigger:m,onClose:g}=s,y=c.current,{onPointerInTransitChange:A}=u,C=w.useCallback(()=>{h(null),A(!1)},[A]),S=w.useCallback((R,E)=>{const I=R.currentTarget,j={x:R.clientX,y:R.clientY},B=I0(j,I.getBoundingClientRect()),L=D0(j,B),F=O0(E.getBoundingClientRect()),M=M0([...L,...F]);h(M),A(!0)},[A]);return w.useEffect(()=>()=>C(),[C]),w.useEffect(()=>{if(m&&y){const R=I=>S(I,y),E=I=>S(I,m);return m.addEventListener("pointerleave",R),y.addEventListener("pointerleave",E),()=>{m.removeEventListener("pointerleave",R),y.removeEventListener("pointerleave",E)}}},[m,y,S,C]),w.useEffect(()=>{if(d){const R=E=>{const I=E.target,j={x:E.clientX,y:E.clientY},B=(m==null?void 0:m.contains(I))||(y==null?void 0:y.contains(I)),L=!T0(j,d);B?C():L&&(C(),g())};return document.addEventListener("pointermove",R),()=>document.removeEventListener("pointermove",R)}},[m,y,d,g,C]),v.jsx(jp,{...r,ref:f})}),[k0,N0]=oi(wo,{isInside:!1}),P0=gg("TooltipContent"),jp=w.forwardRef((r,l)=>{const{__scopeTooltip:s,children:u,"aria-label":c,onEscapeKeyDown:f,onPointerDownOutside:d,...h}=r,m=Eo(Rr,s),g=li(s),{onClose:y}=m;return w.useEffect(()=>(document.addEventListener(ya,y),()=>document.removeEventListener(ya,y)),[y]),w.useEffect(()=>{if(m.trigger){const A=C=>{const S=C.target;S!=null&&S.contains(m.trigger)&&y()};return window.addEventListener("scroll",A,{capture:!0}),()=>window.removeEventListener("scroll",A,{capture:!0})}},[m.trigger,y]),v.jsx(Pa,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:A=>A.preventDefault(),onDismiss:y,children:v.jsxs(op,{"data-state":m.stateAttribute,...g,...h,ref:l,style:{...h.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[v.jsx(P0,{children:u}),v.jsx(k0,{scope:s,isInside:!0,children:v.jsx(y0,{id:m.contentId,role:"tooltip",children:c||u})})]})})});Mp.displayName=Rr;var Lp="TooltipArrow",R0=w.forwardRef((r,l)=>{const{__scopeTooltip:s,...u}=r,c=li(s);return N0(Lp,s).isInside?null:v.jsx(lp,{...c,...u,ref:l})});R0.displayName=Lp;function I0(r,l){const s=Math.abs(l.top-r.y),u=Math.abs(l.bottom-r.y),c=Math.abs(l.right-r.x),f=Math.abs(l.left-r.x);switch(Math.min(s,u,c,f)){case f:return"left";case c:return"right";case s:return"top";case u:return"bottom";default:throw new Error("unreachable")}}function D0(r,l,s=5){const u=[];switch(l){case"top":u.push({x:r.x-s,y:r.y+s},{x:r.x+s,y:r.y+s});break;case"bottom":u.push({x:r.x-s,y:r.y-s},{x:r.x+s,y:r.y-s});break;case"left":u.push({x:r.x+s,y:r.y-s},{x:r.x+s,y:r.y+s});break;case"right":u.push({x:r.x-s,y:r.y-s},{x:r.x-s,y:r.y+s});break}return u}function O0(r){const{top:l,right:s,bottom:u,left:c}=r;return[{x:c,y:l},{x:s,y:l},{x:s,y:u},{x:c,y:u}]}function T0(r,l){const{x:s,y:u}=r;let c=!1;for(let f=0,d=l.length-1;f<l.length;d=f++){const h=l[f],m=l[d],g=h.x,y=h.y,A=m.x,C=m.y;y>u!=C>u&&s<(A-g)*(u-y)/(C-y)+g&&(c=!c)}return c}function M0(r){const l=r.slice();return l.sort((s,u)=>s.x<u.x?-1:s.x>u.x?1:s.y<u.y?-1:s.y>u.y?1:0),j0(l)}function j0(r){if(r.length<=1)return r.slice();const l=[];for(let u=0;u<r.length;u++){const c=r[u];for(;l.length>=2;){const f=l[l.length-1],d=l[l.length-2];if((f.x-d.x)*(c.y-d.y)>=(f.y-d.y)*(c.x-d.x))l.pop();else break}l.push(c)}l.pop();const s=[];for(let u=r.length-1;u>=0;u--){const c=r[u];for(;s.length>=2;){const f=s[s.length-1],d=s[s.length-2];if((f.x-d.x)*(c.y-d.y)>=(f.y-d.y)*(c.x-d.x))s.pop();else break}s.push(c)}return s.pop(),l.length===1&&s.length===1&&l[0].x===s[0].x&&l[0].y===s[0].y?l:l.concat(s)}var L0=Ip,B0=Dp,F0=Op,_0=Tp,Bp=Mp;const wa=L0,wr=B0,xr=F0,Cr=w.forwardRef(({className:r="",sideOffset:l=4,...s},u)=>{const c=()=>{let f="tooltip-content";return r&&(f+=` ${r}`),f};return v.jsx(_0,{children:v.jsx(Bp,{ref:u,side:"bottom",sideOffset:l,className:c(),...s})})});Cr.displayName=Bp.displayName;const pd=r=>{let l;const s=new Set,u=(g,y)=>{const A=typeof g=="function"?g(l):g;if(!Object.is(A,l)){const C=l;l=y??(typeof A!="object"||A===null)?A:Object.assign({},l,A),s.forEach(S=>S(l,C))}},c=()=>l,h={setState:u,getState:c,getInitialState:()=>m,subscribe:g=>(s.add(g),()=>s.delete(g))},m=l=r(u,c,h);return h},z0=r=>r?pd(r):pd,U0=r=>r;function Q0(r,l=U0){const s=Wn.useSyncExternalStore(r.subscribe,()=>l(r.getState()),()=>l(r.getInitialState()));return Wn.useDebugValue(s),s}const hd=r=>{const l=z0(r),s=u=>Q0(l,u);return Object.assign(s,l),s},V0=r=>r?hd(r):hd,He=[];for(let r=0;r<256;++r)He.push((r+256).toString(16).slice(1));function W0(r,l=0){return(He[r[l+0]]+He[r[l+1]]+He[r[l+2]]+He[r[l+3]]+"-"+He[r[l+4]]+He[r[l+5]]+"-"+He[r[l+6]]+He[r[l+7]]+"-"+He[r[l+8]]+He[r[l+9]]+"-"+He[r[l+10]]+He[r[l+11]]+He[r[l+12]]+He[r[l+13]]+He[r[l+14]]+He[r[l+15]]).toLowerCase()}let ua;const H0=new Uint8Array(16);function Y0(){if(!ua){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");ua=crypto.getRandomValues.bind(crypto)}return ua(H0)}const K0=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),md={randomUUID:K0};function xa(r,l,s){var c;if(md.randomUUID&&!r)return md.randomUUID();r=r||{};const u=r.random??((c=r.rng)==null?void 0:c.call(r))??Y0();if(u.length<16)throw new Error("Random bytes length must be >= 16");return u[6]=u[6]&15|64,u[8]=u[8]&63|128,W0(u)}const Fp="mindmap-data",Ca=()=>{const r=xa(),l={id:r,text:"未命名文件",level:1,children:[],position:{x:400,y:300},style:{fontWeight:"bold",fontStyle:"normal",color:"#ffffff",borderWidth:1}};return{nodes:{[r]:l},rootId:r}},G0=()=>{try{const r=localStorage.getItem(Fp);return r?JSON.parse(r):Ca()}catch(r){return console.error("Failed to load from localStorage:",r),Ca()}},en=r=>{try{localStorage.setItem(Fp,JSON.stringify(r))}catch(l){console.error("Failed to save to localStorage:",l)}},ko=V0((r,l)=>{var u;const s=G0();return{mindMapData:s,selectedNode:null,activeMenu:"start",contextMenu:{isOpen:!1,position:{x:0,y:0}},formatToolbarNodeId:null,aiPromptText:((u=s.nodes[s.rootId])==null?void 0:u.text)||"",isAiGenerating:!1,setActiveMenu:c=>r({activeMenu:c}),setSelectedNode:c=>r({selectedNode:c}),setContextMenu:c=>r({contextMenu:c}),setFormatToolbarNodeId:c=>r({formatToolbarNodeId:c}),setAiPromptText:c=>r({aiPromptText:c}),setAiGenerating:c=>r({isAiGenerating:c}),updateNode:(c,f)=>{r(d=>{const h={...d.mindMapData,nodes:{...d.mindMapData.nodes,[c]:{...d.mindMapData.nodes[c],...f}}};return en(h),{mindMapData:h}})},calculateNodeTreeHeight:(c,f)=>{const d=f[c];if(!d||d.children.length===0)return 40;let h=0;for(const g of d.children)h+=l().calculateNodeTreeHeight(g,f);const m=(d.children.length-1)*20;return Math.max(40,h+m)},relayoutLevel2Nodes:c=>{const f=l(),d=f.mindMapData.nodes[c];if(!d||d.level!==1)return;const h=d.children;if(h.length===0)return;if(h.length===1){const R=h[0],E=f.mindMapData.nodes[R];if(E){const I=M=>{const z=M.style.borderWidth||1;switch(M.level){case 1:return z*2+24+20;case 2:return z*2+20+14;case 3:return z*2+16+14;default:return z*2+20+14}},j=I(d),B=I(E),F=d.position.y+j/2-B/2;l().updateNode(R,{position:{...E.position,y:F}})}return}h.forEach(R=>{l().relayoutLevel3Nodes(R)});const m=h.map(R=>{const E=f.mindMapData.nodes[R];return!E||E.children.length===0?40:(E.children.length-1)*60+40}),g=(h.length-1)*50,A=m.reduce((R,E)=>R+E,0)+g;let S=d.position.y-A/2;h.forEach((R,E)=>{const I=m[E],j=S+I/2;l().relayoutLevel3NodesAtPosition(R,j);const B=f.mindMapData.nodes[R];B&&l().updateNode(R,{position:{...B.position,y:j}}),S+=I+50})},relayoutLevel3NodesAtPosition:(c,f)=>{const d=l(),h=d.mindMapData.nodes[c];if(!h||h.level!==2)return;const m=h.children;if(m.length===0)return;const g=60,y=(m.length-1)*g,A=f-y/2;m.forEach((C,S)=>{const R=d.mindMapData.nodes[C];if(R){const E=A+S*g;l().updateNode(C,{position:{...R.position,y:E}})}})},updateLevel2NodeAndChildren:(c,f)=>{const d=l(),h=d.mindMapData.nodes[c];h&&(l().updateNode(c,{position:{...h.position,y:h.position.y+f}}),h.children.forEach(m=>{const g=d.mindMapData.nodes[m];g&&l().updateNode(m,{position:{...g.position,y:g.position.y+f}})}))},relayoutLevel3Nodes:c=>{const f=l(),d=f.mindMapData.nodes[c];if(!d||d.level!==2)return;const h=d.children;if(h.length===0)return;const m=60,g=(h.length-1)*m,y=d.position.y-g/2;h.forEach((A,C)=>{const S=f.mindMapData.nodes[A];if(S){const R=y+C*m;l().updateNode(A,{position:{...S.position,y:R}})}})},addChildNode:c=>{const f=l(),d=f.mindMapData.nodes[c];if(!d||d.level>=3)return;const h=xa(),m=d.position,g=d.level+1,y=d.children,A=g===2?300:250;let C=m.y,S=m.x+A;if(y.length===0)if(S=m.x+A,g===2){const E=(d.style.borderWidth||1)*2+24+20;C=m.y+E/2-36/2}else C=m.y;else{const E=y[0],I=f.mindMapData.nodes[E];if(I&&(S=I.position.x),g===2){const j=y[y.length-1],B=f.mindMapData.nodes[j];B&&(C=B.position.y+100)}else{const j=y.length+1,B=60,L=(j-1)*B;C=m.y-L/2+y.length*B,setTimeout(()=>{l().relayoutLevel3Nodes(c)},0)}}const R={id:h,text:"分支主题",level:g,parentId:c,children:[],position:{x:S,y:C},style:{fontWeight:"normal",fontStyle:"normal",color:"#000000",borderWidth:1}};if(r(E=>{const I={...E.mindMapData,nodes:{...E.mindMapData.nodes,[h]:R,[c]:{...E.mindMapData.nodes[c],children:[...E.mindMapData.nodes[c].children,h]}}};return en(I),{mindMapData:I,selectedNode:{id:h,level:g}}}),g===2)setTimeout(()=>{l().relayoutLevel2Nodes(c)},0);else if(g===3){const E=f.mindMapData.nodes[c];E&&E.parentId&&setTimeout(()=>{l().relayoutLevel2Nodes(E.parentId)},150)}},addSiblingNode:c=>{const f=l(),d=f.mindMapData.nodes[c];if(!d||d.level===1||!d.parentId)return;const h=d.parentId,m=f.mindMapData.nodes[h];if(!m||m.level>=3)return;const g=xa(),y=m.position,A=d.level,S=m.children.indexOf(c);let R=y.y;const E=d.position.x;A===2?R=d.position.y+100:R=d.position.y+60;const I={id:g,text:"分支主题",level:A,parentId:h,children:[],position:{x:E,y:R},style:{fontWeight:"normal",fontStyle:"normal",color:"#000000",borderWidth:1}};if(r(j=>{const B=[...m.children];B.splice(S+1,0,g);const L={...j.mindMapData,nodes:{...j.mindMapData.nodes,[g]:I,[h]:{...j.mindMapData.nodes[h],children:B}}};return en(L),{mindMapData:L,selectedNode:{id:g,level:A}}}),A===2)setTimeout(()=>{l().relayoutLevel2Nodes(h)},0);else if(A===3){setTimeout(()=>{l().relayoutLevel3Nodes(h)},0);const j=f.mindMapData.nodes[h];j&&j.parentId&&setTimeout(()=>{l().relayoutLevel2Nodes(j.parentId)},150)}},toggleNodeCollapse:c=>{const d=l().mindMapData.nodes[c];if(!d)return;const h=!d.collapsed;r(m=>{const g={...m.mindMapData,nodes:{...m.mindMapData.nodes,[c]:{...m.mindMapData.nodes[c],collapsed:h}}};return en(g),{mindMapData:g}}),d.level===2&&d.parentId&&setTimeout(()=>{l().relayoutLevel2Nodes(d.parentId)},0)},deleteNode:c=>{const f=l(),d=f.mindMapData.nodes[c];if(!d||d.level===1)return;const h=d.level,m=d.parentId,g=(y,A)=>{const C=A[y];if(!C)return A;let S={...A};for(const R of C.children)S=g(R,S);return delete S[y],S};if(r(y=>{var R;const A=g(c,y.mindMapData.nodes),C=d.parentId&&A[d.parentId]?{...A,[d.parentId]:{...A[d.parentId],children:A[d.parentId].children.filter(E=>E!==c)}}:A,S={...y.mindMapData,nodes:C};return en(S),{mindMapData:S,selectedNode:((R=y.selectedNode)==null?void 0:R.id)===c?null:y.selectedNode}}),h===2&&m)setTimeout(()=>{l().relayoutLevel2Nodes(m)},0);else if(h===3&&m){setTimeout(()=>{l().relayoutLevel3Nodes(m)},0);const y=f.mindMapData.nodes[m];y&&y.parentId&&setTimeout(()=>{console.log("重新布局所有level2节点，因为删除了level3节点"),l().relayoutLevel2Nodes(y.parentId)},150)}},updateNodeStyle:(c,f)=>{const h=l().mindMapData.nodes[c];h&&l().updateNode(c,{style:{...h.style,...f}})},toggleBold:c=>{const d=l().mindMapData.nodes[c];d&&l().updateNodeStyle(c,{fontWeight:d.style.fontWeight==="bold"?"normal":"bold"})},toggleItalic:c=>{const d=l().mindMapData.nodes[c];d&&l().updateNodeStyle(c,{fontStyle:d.style.fontStyle==="italic"?"normal":"italic"})},updateBorderWidth:(c,f)=>{l().updateNodeStyle(c,{borderWidth:f})},updateTextColor:(c,f)=>{l().updateNodeStyle(c,{color:f})},resetData:()=>{const c=Ca();en(c),r({mindMapData:c,selectedNode:null})},loadData:c=>{en(c),r({mindMapData:c})},addAiGeneratedNode:c=>{const f=l(),{id:d,text:h,level:m,parentId:g}=c;if(m===1){console.warn("跳过level 1节点，不能替换根节点");return}if(!g){console.error("parentId不能为空");return}const y=f.mindMapData.nodes[g];if(!y){console.error("找不到父节点:",g);return}const A=y.position,C=m,S=C===2?300:250,R=y.children,E=C===2?100:60,I=A.y+R.length*E,j=A.x+S,B={id:d,text:h,level:C,parentId:g,children:[],position:{x:j,y:I},style:{fontWeight:"normal",fontStyle:"normal",color:"#000000",borderWidth:1}};if(r(L=>{const F={...L.mindMapData,nodes:{...L.mindMapData.nodes,[d]:B,[g]:{...L.mindMapData.nodes[g],children:[...L.mindMapData.nodes[g].children,d]}}};return en(F),{mindMapData:F}}),C===2)l().relayoutLevel2Nodes(g);else if(C===3){l().relayoutLevel3Nodes(g);const L=f.mindMapData.rootId;setTimeout(()=>{l().relayoutLevel2Nodes(L)},50)}},clearNodeChildren:c=>{const d=l().mindMapData.nodes[c];if(!d||d.children.length===0){console.log("节点没有子节点，无需清理");return}console.log(`🧹 开始清理节点 ${c} 的所有子节点...`);const h=(g,y)=>{const A=y[g];if(!A)return y;let C={...y};for(const S of A.children)C=h(S,C);return delete C[g],C},m=[...d.children];r(g=>{let y={...g.mindMapData.nodes};for(const C of m)y=h(C,y);y[c]={...y[c],children:[]};const A={...g.mindMapData,nodes:y};return en(A),{mindMapData:A}}),console.log(`✅ 已清理节点 ${c} 的 ${m.length} 个子节点`)}}}),b0=[["#FFFFFF","#F8F8F8","#F0F0F0","#E8E8E8","#E0E0E0","#D8D8D8","#D0D0D0","#C8C8C8","#C0C0C0","#B8B8B8"],["#F5F5F5","#F0E6E6","#F0F0E6","#E6F0E6","#E6E6F0","#F0E6F0","#E6F0F0","#F0F0E6","#E6E6E6","#D8D8D8"],["#D3D3D3","#FFB6C1","#FFDAB9","#F0E68C","#98FB98","#87CEEB","#DDA0DD","#F0F8FF","#FFE4E1","#B0C4DE"],["#A9A9A9","#FF69B4","#FFA500","#FFD700","#32CD32","#4169E1","#9370DB","#40E0D0","#FF6347","#4682B4"],["#696969","#DC143C","#FF4500","#DAA520","#228B22","#0000CD","#8A2BE2","#008B8B","#B22222","#191970"]],_p=({color:r,onChange:l,onComplete:s})=>{const[u,c]=w.useState(r),[f,d]=w.useState([]),[h,m]=w.useState(!1),g=w.useRef(null);w.useEffect(()=>{const M=localStorage.getItem("recentColors");if(M)try{const z=JSON.parse(M);d(z)}catch(z){console.error("Failed to parse recent colors:",z),d(["#FADCDB"])}else d(["#FADCDB"])},[]);const y=M=>{localStorage.setItem("recentColors",JSON.stringify(M))},A=M=>{const z=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(M);return z?{r:parseInt(z[1],16),g:parseInt(z[2],16),b:parseInt(z[3],16)}:null},C=(M,z,U)=>"#"+((1<<24)+(M<<16)+(z<<8)+U).toString(16).slice(1),S=()=>{const M=A(u);return M?[M.r,M.g,M.b]:[0,0,0]},R=M=>{l(M),c(M);const z=[M,...f.filter(U=>U!==M)].slice(0,10);d(z),y(z),s()},E=M=>{const z=M.target.value;c(z),/^#[0-9A-Fa-f]{6}$/.test(z)&&l(z)},I=(M,z)=>{const U=parseInt(z)||0,J=Math.max(0,Math.min(255,U)),X=S();X[M]=J;const oe=C(X[0],X[1],X[2]);c(oe),l(oe)},j=M=>{M.key==="Enter"&&/^#[0-9A-Fa-f]{6}$/.test(u)&&R(u)},B=M=>{M.key==="Enter"&&R(u)},L=()=>{m(!h)},F=M=>{M.stopPropagation()};return w.useEffect(()=>{g.current&&g.current.blur()},[]),w.useEffect(()=>{g.current&&setTimeout(()=>{g.current&&g.current.setSelectionRange(g.current.value.length,g.current.value.length)},0)},[]),v.jsxs("div",{className:"custom-color-picker",onClick:F,children:[v.jsxs("div",{className:"color-section",children:[v.jsxs("div",{className:"section-header",children:[v.jsx("span",{children:"系统色"}),v.jsx(vo,{className:"dropdown-arrow"})]}),v.jsx("div",{className:"system-colors-grid",children:b0.map((M,z)=>v.jsx("div",{className:"color-row",children:M.map((U,J)=>v.jsx("div",{className:"color-swatch",style:{backgroundColor:U},onClick:()=>R(U),title:U},`${z}-${J}`))},z))})]}),v.jsxs("div",{className:"color-section",children:[v.jsx("div",{className:"section-header",children:v.jsx("span",{children:"最近使用"})}),v.jsx("div",{className:"recent-colors",children:Array.from({length:10},(M,z)=>{const U=f[z]||"";return v.jsx("div",{className:`color-swatch ${U?"":"empty"}`,style:{backgroundColor:U||"transparent"},onClick:()=>U&&R(U),title:U||"未使用"},z)})})]}),v.jsx("div",{className:"hex-input-section",children:v.jsxs("div",{className:"hex-input-container",children:[v.jsx("svg",{className:"eyedropper-icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"5245",width:"16",height:"16",children:v.jsx("path",{d:"M881.777778 142.222222c-51.2-51.2-133.688889-51.2-184.888889 0l-108.088889 108.088889-42.666667-42.666667c-39.822222-39.822222-102.4-39.822222-142.222222 0s-39.822222 102.4 0 142.222223l42.666667 42.666666L73.955556 768c-5.688889 5.688889-8.533333 14.222222-8.533334 22.755556v153.6c0 8.533333 8.533333 17.066667 17.066667 17.066666h153.6c8.533333 0 17.066667-2.844444 22.755555-8.533333l372.622223-372.622222 42.666666 42.666666c39.822222 39.822222 102.4 39.822222 142.222223 0s39.822222-102.4 0-142.222222l-42.666667-42.666667 108.088889-108.088888c51.2-54.044444 51.2-136.533333 0-187.733334zM213.333333 878.933333H145.066667v-71.111111l349.866666-349.866666 71.111111 71.111111L213.333333 878.933333z",fillOpacity:"0.65","p-id":"5246"})}),v.jsxs("div",{className:"mode-toggle-container",children:[v.jsxs("div",{className:"mode-toggle",onClick:M=>{M.stopPropagation(),L()},children:[v.jsx("span",{children:h?"RGB":"Hex"}),v.jsx("span",{className:"toggle-arrow",children:"↔"})]}),h?v.jsx("div",{className:"rgb-inputs",children:S().map((M,z)=>v.jsx("input",{type:"number",value:M,onChange:U=>I(z,U.target.value),onKeyDown:B,className:"rgb-input",min:"0",max:"255"},z))}):v.jsx("input",{ref:g,type:"text",value:u,onChange:E,onKeyDown:j,className:"hex-input",placeholder:"#000000",autoFocus:!1,tabIndex:-1})]}),v.jsx("div",{className:"color-preview",style:{backgroundColor:u}})]})})]})},J0="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAAwElEQVQ4EZWRIRLEIAxFuVNUFQ7FBarWVaFwVat6lF4AhaviAqvqUCgugMvO7rQQpnSZ/SYR/8FPwvBPsYY/GCWgklAmHMYGENYRQE7PrEkCjOtBMERv54ckEhwAZle+dnMFvBYOMPBC9IAPXz3Yi3QBsDP0FSjpGx3DGyDF3VlrN7fHRLk24KtLCGV8ZlrAd2qhTUiIKRgtyFaR4WWtkg8A2uYgyWpCMMS4LdXhukCOdzadSKeN1p9DUyPp79b6BuKVNtThLRWfAAAAAElFTkSuQmCC",X0="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAMCAIAAAACrGGSAAAAP0lEQVQ4EWP4Txlg+P///4slntqkA88lL/7//w/VD+EQ75QXSzxH9Q+j8NM2siINGGkj4v///+/vSQffwakNAE3DcqI5WhbCAAAAAElFTkSuQmCC",$0="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAATCAIAAAD5x3GmAAABMElEQVQ4EZ2QLXOEMBCG7z9FoeqiouJQcVWncCgUDsUfQOFQp+KYyQwOhapLTDE3cyKqbjv5WL56V6Zdk2yyz7677wWWmGrGrjftc9uXLCGEYr4UuctlzeyQU1fWjb2/iEz4PLZZCzcMAASMLAI20Ae13xnX5ie2YY6zFcNXmCdgrP6I4yGDAHowtPVwxxV0mxLyVk575tO98iaYhqXh9AChxWDxHXWmkiZp1VSCs03wrO5yvgN2XuvblSaibpzPIdKqLXkSFew04RSo43W32A4AKzNC0IUdAwAOI2klu5yiAgD4dQkhxeB6HxkAuPelYPy9HpelAexYCZ5FU58waM/LMzLzPCul5FkopeZ5joxSyhjzOAtjjFIqMlLKs/r4L6X8m47WetX5zz4vPXr28Q3njQl0+p0j+gAAAABJRU5ErkJggg==",Z0="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAAlUlEQVQ4EWP4TyJgIFH9fxwavl9cXRZohQzaTkGMxqrhYp2RUf7mB++RwXeoU7BpuFhnlL4Ll1OxaThUpl12iMoaviO79v2ufAI2vFidgBweVkbaBDSgO5cWfkC1g2QbbvZaRax+j2oInIctHv6/35xg5Ji/ZBcyuAk1AauG////f795aNeStjIE2HwTYgkuDXAnoDMA6qS7ZJO9M3wAAAAASUVORK5CYII=",q0="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAIAAAB/UwMIAAAAy0lEQVQ4Ea2SsQrCMBCG75GUA9GE0E4Ovsc9iEuLLgXJkGwBIwHxMVw6+C6O3XSoNMdJi4o3/X/If5fkCzy+L4gxGmOQlTEmxjjRCrTWRORYEZHWeiqDiN57vsN7j4h8RWj4JaOU6s/WWNtY65wjIqWU6M0tVFXF7v+SdV3zTULD4MsUyhQGOyH+mhHcOLTROYIbh5Yzm8tpHjIowYBDy5nDrYX9dhZckUKRwkeZe9ft2uv6fOwzi9WS/ykOLc8Rj/vObYA2mhEtuH0CnpiE/1iGGeYAAAAASUVORK5CYII=",eA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAIAAAB/UwMIAAAAlUlEQVQ4EWP4/3RTjpsxOnDL2fT0Py7A8HRppLGxT1o9MkjzMTaOXIpTE0RP5VEUM49W0kgPiW4jJwxgHgGHhTFef8CUMsAYVNGDGW+wSMNpD0a8ISINruf/pVZ79NRgjBxviEhD6Pn/dE28JZougnr+////4+mZ/VCwptIHNU1hcxssAGE0EWEAU0oEjeQfIlRDlAAAFshok8eY7o4AAAAASUVORK5CYII=",tA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAIAAAB/UwMIAAAAz0lEQVQ4EZ1SIRLEIAzkT1FVfOA+gOIdPADXb9T1AXWouqqqOlRVHAqXm8C1A8O1c9e4TbJks0RQjugX55xbfGSMGwO3IYO6RESCs35QkEMNOydmk6CZGexF1XNCEOGoAVS/BsaXEdZeAegRE4cf/bx/yeBCmsejRRaSOH4y+vUttJl8zYkBMUSi1UqATjYk2QFIuxIdjcmDLCktnteuNDb5h5xj5D/aTt/Q2QsPrMPag5NTbdGC0usnf/rkdpKK+hB/uNFW+22m+J/bvrL4Bgguby2J3zpdAAAAAElFTkSuQmCC",nA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAATCAIAAAD5x3GmAAAA9ElEQVQ4EaWRLRLCMBCFudOqqIjMdBBVMTtROBSqrgpVh+ICKBwKG1FVV9ULoLjAKlyY0AW2pZ0WWJPs5H15+7MI38diHKF65zSASrPzpaMaY6jaJgBgN/k6nu4osEGGARa+siu7fTIvifiZylyDLpoW6jFDQAhU5Umsj40kMwY8OhMdCeZ6dADuIEqKDn0ghCCYW5nFCe0b4l4HgS5Dzc5BDFvUNOzQnQE1+xZ4UKB13OZKboXNnz5UFbbVAiSbU3nKbGr72+8x73TWLc7Ae4+IaioQ0XvPtSGiMWY5FcYYRGRGKTWl53el1B8+v/Qza1hCdAdjYbSnkf1K/gAAAABJRU5ErkJggg==",rA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAA5UlEQVQ4EZ1SIRKEMAzsn6pQOFQ/wB/4AKoKh+IDKByKD1TVVaFwVVU4VN3epOWuDMwwBzGEbbrJbsMQwjujlFLLCsBbyo31ANaFYOMoj8EAbLoueIhaA3BDyTkvBwdA1/GgqPX2uzA3OU/Al+nwDYR5MweIRcJAfag5pdQotgQD/LauWxryVBt+DyWk4VEwuLESojW3t0wrRDWSC3ij4Zb7cvhGA73TI1uDyb29NE+A7RMnjWSpR5aL3axg224K2SPyjJ5tZ4wavFOdlFJOCy2c7qWUvaZFXCaCO5W274XoNOpf2QdidaCUHFDB1wAAAABJRU5ErkJggg==",oA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAA7ElEQVQ4EY2SIRaEIBCGvRPJtI20FzDZSCQbiQvsHTbZTCSaieYBtplMNJKNfTCMgrjv7QR98P5vZv5hGv8r9m3RWpuPKwVNecSTmwUlKbpxxWvv/R0AasrV6p2RAcyYCsjUkPfClEClrpkMAPWDTXnL2L0zwRRTFgFQB5tUzJfBRMgpHrxsEdijteByVZzeMKlVYVycklWMkO6dOkHGYJ2kHrQNpUKFfR7KrBlTqo93QMXZPdy0bUsITbnBP5r2FWMnFkZQqI8KACemF1JK0cfVeDJ5xmtc7HU1gMEtqv78fAeo8cf38PCHNkq+nSdTrZqSZ/0AAAAASUVORK5CYII=",lA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAA/ElEQVQ4EWP4////vZe/7eqedG54/x8Gtm/fPgMVbN++HSLJcP/Vb5Pyx/KZD+QzHzSufgcRDQsLM0YFgYGBUA1W1Y8tqkAaLMHk+pNfYNZgpxlWH//y8PVv+cwHfZvfLz38+dm7P9gVwkQZIAyIBpggPhqhoX/LB3wKYXLYNURERKD62RjhaYhO+cwHE7YibNi5cydqqM5ABCtWDTD7sdAIJ01EsgGLQpgQg3Pj00nbPshnPihd9Mau7smWs19hUthphoQpLyHRDCHP3vvx//9/fH749vNfcM9ziOpdF79BjCUQSt9+/sud+3o3TDV2p8BEoZ6GcQnTJGsAAOoufwILeW1QAAAAAElFTkSuQmCC",iA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAA/0lEQVQ4EdWSQUoDMRSGc5NcoZChLbQHyC2yySZ7YVrrvoji8tHcQBHU0TOkRXqIhskgWG1HaHdTeDLONOp0rN328RYvf/6P5A8hiGiMEUKwP0oIYYzBbRFElFJyzgFgtFMAwDmXUm79mAOMMQDwUmUAAMaYF0tAa+2lyqC1PgKgcun9yzw0IkbT9evHZtf6stxE0/VPPQeG90uq7N3zr43CdDNeUWXPH1LPkIsopcoGobt6Smu7cRJTZS8fS4Z0BglV9t/uniXFIWQ2z5o9F4QuWdRkiN+zIHStvovfshJAxNk8a/Xd7aQmw7VZtU+/3Yhff8kHOmQon/UQa+H5BGyGeQ1H8Lf4AAAAAElFTkSuQmCC",zp="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAIAAAB/UwMIAAABCklEQVQ4EZWRLZPDIBCG+59WRZ1DoXBRuFNVcVFRcSj+QFRcVFVdFC4Y1LmoUzjUub3hc6CdTqdrYHd49t19ueDncfkcwZoxktLr7Qxd3D7TDoDkvG5dM06NxD/bjj1c+MBDntoUrGYQMWJQBFykW7U3jG/zhNXM42yT+ovzRIzKn5gWJgPZA7VKZfMK59oDfM2mZX59lS3RtPw0ngEAMimX6kXHzKTrxSI4o1WwQW4jqwHE+n/O25V0XC7e5xi9WGfWJQVnTJqi6ATdGmsAdPcBILnQMojoMejFfRtJVkDEsC4ATKqdrWxu95lT9i2PvLT/pUNwNkRTn3QK+fqSGGut1lq9C621tfYfj3KLLE14ur0AAAAASUVORK5CYII=",Up="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAPCAIAAABiEdh4AAABFUlEQVQoFZWSoZKEMAyGeaeoqnVVq3CodSgUrgq1blVfAIVDVdVV1Z1C4apQdVV1uUl3szDc3txcTOif/OkXoMJ/RrX3p0XfBDRT2KXlIYXspiXtEhuC6SSAEAJA9mbLiHmdSSohO8NjiiFMN2ocXMrRKu4CknzCaHsJIJUv91SIYWz2MyLmuPpZ69mvb5LkaEyhraLpAKC3ecf89JRMS20uV/ikB9lp+0Xs50jB2wchg1QuYtkhb26oabve/XBsUwMAor67WEbxW8Iw1gDKn+cjekX0GxfehpPOdcRVX+FyX1hgQ6SlWvO8loslFyblWGPDr0RnJjYQ0aXphw/RXuHAxIZFHz7w64c4pHpcX0xsYMQ/8zfn9Sudq6V0owAAAABJRU5ErkJggg==",Qp="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAQCAIAAACUZLgLAAABK0lEQVQ4EZ2SvYqDQBDHbUPI00ypkJdQfILAcdWqhYXFJUiyS4RdwReIjWlcVPQKr7K4kCJPoC9i6R3nZRUid8FhimXn/5vd+ZC6WSbNorpfjHOOECKE5Hnetu1jrrZtsywjhCCEOOcDpmka/JiiKFEUjckwDGVZ7qOapg2YENV17bouADiO01+apgkAGOOmaYRsurY0TQEgCAJKKQCUZSmA/jCB0dt1xbB03AlfMUxv1zE5gS3ZYX0+vX1Wwtfn05IdJjDO+eZu349sL9VYtL1U0nF3j2+GlszExrkXdP/4yQXdjzUTtb2UhWiGOLx+vP+DdV1XFAUA+L7PGHtqAHVdY4wBwLbtPr1lWQDged7EuDnnqqo+s1y6rg+djOPYMIy/VzlJEoxxv8pf4/adJNLnLdAAAAAASUVORK5CYII=",Vp="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAIAAAB/UwMIAAABG0lEQVQ4EZWSoZaFIBCGfadJN9FIJhvJtmkTjbTJZuIFTLRNJpuJZiLRSCYaiTb3yIqKe+7uuRSdOf83DP9Mhe+fqkCimyVvKYF0CG25nF0sJIh4Mn4SFB6NUNp5H2IMfrWzEs0DqJj8lduZoAV9tOp3TcRgJAMqdDiwxISJAxvcntx4ACCsX7LOyhr4lKONsbIm3ZLL6C8gfHRu5KSWds9GLc6oQlwVI53JCEZnXPjpqP0+32E6wtSaVBWiFpCjDPrxAygvXr4qBkL/wZie0v68elOWzK23fNftuxS9JQ+EvozODg1AM2QDEPHuAeLmNb3YcGeS+6XXiJiyTJo8gktjL2aaFMfuzHY9dkd+Uni1O3vdd3f00s4/v08VrqfB/CYIdgAAAABJRU5ErkJggg==",sA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAIAAAC0D9CtAAAA7UlEQVQ4EaXSKxKDMBAG4N7pV6i4qqi4qDoOgMsB4qJyBy6AwkXhqlA4VBUuCredJm0X+pqWRjBh2W/ZyWZHv6/dI4lDqJ0plZSqNK4OQ3zMoKWJnVMFABRCpiXym3LdCt5N7MweKJQN48yV5zHYS529WbCrmZoSEFUzcTrvpqYSQHn/mM3RCqBqVx2wIYptBQh7zLFkeis4sszl/aWqsH0KJBMMoOsTZzzvTrUGTPjPfNmb9AP/hzacAdGGsyaizzMFtOtvo7jONPX57u4cfOM1AO0zW5oMX9/R2DN7Mgm+fGQm/fCDIaI5TnGmM5GQyqpc2R6uAAAAAElFTkSuQmCC",aA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAYAAAA7bUf6AAAACXBIWXMAAAsTAAALEwEAmpwYAAABNUlEQVQ4jbWSLVDDUBCEtwyDKio1xQSHIeqp1hQVEGCCqsQUQw2o4iriiqmrIobBIRoDmOJiGhMMrlFEhJiAYGIWESaQH0JbhnW3b+Z7d3sHphRyqmsU6oAO5xdyACHYvpotgEgg3wCXM74vhCArJPlidrHXt1CuKqTtJjpnPRwq66mXCknCNdE97sMK1iA3VChSESSCZz/A9iLI2gjGuUCCSnqajXmiCgq1x0n4U+MhnWGbQgh2xn42kyyobDshb08FRUvntBBCkr7DiePn7JQcnS2xz+FTXK7kRq8p2FFq5RlLm9iABy+IyzxkHgUunlFHXVoa8oo74wZv1QaaW59W+fD5UIu2s1r4l2vCMGwEKTeCZ93DDgBZG2Fw8JVbfGwZPV7s4ug6yLi/Xewftdx2/gPyAYSR87E8j04fAAAAAElFTkSuQmCC",uA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAYAAAA7bUf6AAAACXBIWXMAAAsTAAALEwEAmpwYAAABIUlEQVQ4jb2SIVPDQBSElwxDDVWNAYWqQz0VRdVhUoMLKj8AhUN1KkBlEBTPIILDgMlgAoLBBFMQuFDRmg6mINqaxZQSmkvaVPDJe3ff3OxbcFHaHpXs8TgapEZYWMKYviMUjaiAhOQw5oVGtEKSmDJC7/4czdYtnjtfyMdCI2ihbgKrv4fvuHL3cfo6xnp1B7Zd1j79eLnDU2eMiqWwbU4OJ//kY0NRRLERpoP7YRAeUYlQHdwwTmXydkZbhI7fzc6j7WkFU0nXdyhyyGCYl2qfURCxr5kkMimhXMoL0oTsmtqJkdzM52jOQjIwAGBTLGzgAZfXveUsRbaTRaJsf3tSq870pCJw3Tq2NPUp0Ng1KC/ESS2d/oxkOYz5V/5J8g2XBO+OnY5R0gAAAABJRU5ErkJggg==",cA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAIAAABbzbuTAAABQklEQVQ4EZVRodqDMAzknU6hcFVTuCkcqgpXhcJN7QWmcCgUrgo3VTWHQtVV4bIvoTC2f79YTAvNXe4uCf1YyY/99A9gCT4skcvbJk8BKN268A0w9VqBS5nBL9PtDKjC1DoHsub+OWEZawWoqrVtpYA0TYFzO/Ewa/j6AQhjfWJy6fEDY8reEwV3LQQ674DF3zvDWtOiEkmCmZwLsTktri4QJX4wecaeuFZfREGExTlWtJXdLCEkrsmAk74N48NvufDLG0a0qcYRUUKP6wkoOh9DPBxHjLsooB4ZQGGogMzYcOjdrhGTl4UCNNsX03N7BlB+m0Ik3LyUlTGmFEbD8f8ZsyarjN397bFSfNPdtHkPdmUZj2JfAEmmyWO4/tHxNpTuZcubp+jh9UnL3DPtupX6SxBvEzZgcN3l0u/Stt9yPgFH1nSEm9FKWQAAAABJRU5ErkJggg==",fA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAAVElEQVQ4EWP4TyJg+P//9ZmlPfWEQc/SM6/////P8P/1phxj4kDOptcgDSSCQanhx6VJ8T6YIH7SpR/YvMfwn2QN2IzBIzYoQ4n0pEFq4sMTItikAK6rdEF3OMdNAAAAAElFTkSuQmCC",dA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAAWklEQVQ4EWP4TyJg+P//9ZmlPfWEQc/SM6/////P8P/1phxj4kDOptcgDSSCwa3hx6VJ8T6YIH7SpR9I/kTyA8kakIzBw0SyAY8qJCl6aCA9aZCa+JA8RAwTAMsAdEGkgQYdAAAAAElFTkSuQmCC",pA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAIAAAB/UwMIAAAAWElEQVQ4EWP4TzpgAGt5fWZpTz1h0LP0zOv///+D9bzelGNMHMjZ9Bqqh0TnQdxGmqYB1vPj0qR4H0wQP+nSD5xhQI4evIEywGFA2G1kpR0y0ihel2BKAgBSGKQRq0h5DAAAAABJRU5ErkJggg==",hA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAAzUlEQVQ4Eb1Quw6DIBRlNcafKRuE+DU0TX+iOgiJA6xl60DiD7RDOznUOPhRt/VFkKjp1JM7nHvg3BcCAGstYwxvgDFmrYUZCAAopZzz6wY455TS+T/0BoyxMcZJATHGYIyd+B8DIWR/B0LIYiQhhH+hwwBfkVIuDC4ZyXFAILq0XzrATwbVtYmWqMyDSLRUXetXnDrEWqTV7fKug0irW6zFiuFbOGtq/2HkWVOjMvf1qUOkiq0OkSpWDKfnPZjepefXY8XgS/t85az7hg/NsUZIVRkcxwAAAABJRU5ErkJggg==",Yl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAIAAAB/UwMIAAABAklEQVQ4EaWRsWrDMBCGtYaQl/FtEkKDn8IPICh9gm6tMZHAg71WWwZBXiCFtlDw0JIhGq0X8ItcCRhxURpD6M8Nv+C+X3cSQ8RpmvAesRgjAMQYM8p7L6UEIiml9x4RWQgBAEIIGSOE0Fq/EmmthRAzUxRFVVX6UgDgnKNBzjkA+Afz52yc8+vZOOdL+xhjyP6ztdYuMXSTzN98t6yPHm8y3em46S1r61Sb3nan43m2cRwBYBgGmoSI696o/e75e0il9rt1b84MIpZlqZS6/B7N2vrp650GvfwMrK1nJoTQNM19DA1LftVtr2dbddv5ntRHzcPHIW2fzOPn2xJD+cz/AmtDVZCJzzRPAAAAAElFTkSuQmCC",Kl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAA7ElEQVQ4EbWQwYrCMBCGe9QVX6ZzSwg+TUB8gF41sDYgbHs1Nw8BX2D3oKceFA/Ji5Q+xL9butQYgrAsDkOYmfDNzD8Z/mgZAGutEIJSJoSw1oY9e4BzLqXcp0xKyTmPASIyxoTVMTbGENGYAugnvB5gjD3RwBiLVyrLMs9zIhre6Fpa6xgA4L0nIu99+JeMe9H/BSp3m9c626nQ57Wu3O33rAC6riMipZRz7q3aLo6H9aUJfXE8zOryDgAoimKQ+9N4c20iAZtrk+3UAwCgbVvn3OTjPTlhWm1jYOi6PH2G24/x6vyVBqJlovQbDglDvz8oSeIAAAAASUVORK5CYII=",mA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAIAAABbzbuTAAAAk0lEQVQ4EWP4TyJgQFX/42hrkA8YxBdN2v/0B6osiIem4RNcg5u9sbFx5NKn6FrQNCBJf9qUg00Hbg3/j1YaGxtXHkUyA4uTkGVJ11APsmE/shmYnkaW/XG01c3Y2Dio9egnJGE8fvj///+P+2ty3IyN3VovwbVQUwPJTjpKqqdJD1YSI464pIFIrUQmPkRqxZW8AbDy1flj1/0FAAAAAElFTkSuQmCC",gA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAARCAIAAABfOGuuAAAA3UlEQVQ4EZ1SLRqDMAzdnaJQvcAuUNVz9AB1XAPHAXBVOFQVrgpVV1WXfU0poyuMjQj48pLXvPw88JY9MivYSWutJxsi4ubo6NlFpwwRIdFsxyEZ75aIj5JcOUZn2UUtsTDSXC8AeGt8gk6+3rQcQPQkINLi02uVE0qCqWoSUNDsIMXzyIQc7Cr2TQveOR8QjWIADat4rAFgyiDmRKTesjKaQ3otQ/Q/wNMkU9ZB+Bst1/5T5DZJp9XJSJR21Ug2WtFR7Xws4Oa6EW8dF8kp7/W3U677uED2e7tI3YdfxjjUx8EYHoYAAAAASUVORK5CYII=",vA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAIAAAB/UwMIAAAA40lEQVQ4EZWSIRqDMAyFudNTVbgcoK6qDoXCVaHmUFxgCscBcFU4FGoOhZpD4bKPDD4QHbCYmv7JS96L+P+KmHnqCq1wXkoX3bS2j3j2GUDW5WflLAGZn4WKeKwM4NoLia0DTDVeMu/GEZR+vpj5LtPmsqO0v8vwNPim6d+LoJ/MPPoi0Top/Pjddl8yxPipr1ICEBPFAMg10n+jAowIV/ohA+ahFtyU/WZJSBsorfYPi899aRVAaT2IzMCcoD9y61XmXYaZV5nKWnP09DI7mVnDuGdHMmqWW51VbI4Z3a55//0AuUVGlz1MZEcAAAAASUVORK5CYII=",yA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAIAAAC0D9CtAAABNElEQVQ4EYVSLZfEIAzc/xRVhUOh1lXVVZ2qQ62qq+p/wK2qqqvCnULhqlA4FI59BPrBHfcuKo9kmMwkj/BPeGettc7f2h63/Gfq1NwTyNEwvtrU8SfGSU4Bmue4aGN3OX9RAMqlCyHUMRnQif3itksPwGZdx2C1KQARakQLMKy+xqNnFmtxiiLwncsqj+QArTBFfwh7ZIFh8yXGyfH5nJVCns0HNdEM3pebBddsTr6iT93bpMHppNzGSfxaRwrKt2Pa7Bvqg/bwyapNxWWokUSrvNH6AJxeRwmEy/uyUU58R3sLcciDLNGRMlA2Gb/L1+wBLqR759NILaXsApb0uHUAIL3Y03TeLAOeyim7ggkh2QYNYYyRBgDoqw44PcBvvJFi4n3X80lI88uPi6p+o1e9ln0AqzrNyXPq9jkAAAAASUVORK5CYII=",AA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAIAAAC0D9CtAAABUklEQVQ4EWP4TzpgIF3Lf5x6cnJyioqKsJqIXc/Ro0eNweD48eOY2rDrKSws9Pb29vLyKi4uJkrP8+fPjY2N58yZM3v2bGNj49evX6Npw2LP+fPnPTw83rx58/r1a2Nj45kzZxLWg6yira2tqakJWeT/f6Rwu/H0V9681yblj3WLHoX0PJ+x6+OVRz+RVTeufmdX9wShZ/aej/KZD7QKHmbNflWz/K1jwxP5zAfymQ9sap+ULnpTv/KtWcVj+cwHTavfQfX8+PVPu/Bh9pxXn7//gxt87cmvWXs+hve9MCh5JJ/5IGriixO3fkBkUcJg+fLlycnJcG24GCh6XFxcKisrMZWuXLkyLCwMLo7Qs3PnTmNj4zNnzsDl4Izz588bGxvv2LEDIoLQk5aW5u/vD1eHxggMDMzMzETX4+3tvWLFCjSlcO7WrVuDg4PR9cClCTIApBv6+3seMLIAAAAASUVORK5CYII=",wA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAAhUlEQVQ4EWP4TyJgAKv/8enTDyI1gjU8XRpp7Jaz9NInIjTBNRgbGyM0fbq/aVI9GuhZeub1////UTQYG7tV7geJXupxAxmABnI2vUbRQJyjwDa83t9aSZwPoDYQ4Ve4ErANcB4RDPKcBIoHpEDFaw8lwYpkBzERR1wswIKV1MSH15dokgDEJpFxiZiBjwAAAABJRU5ErkJggg==",xA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAARCAIAAACw+gCQAAAAfElEQVQ4EWP4Ty5gIFfjf5jO12vijZFA/JrX//8/3VQU5IMO4idd+gG2Dabz///7SyNhWiOX3gdJEqsTrheqj5A3EHaCVH46s3TpmU+E9EDkUXUSp4ewTuL9iW4j+TrRTULlU+5PbCkB1Q50HsJOaqYEUkJo4FMCepig8gE3+zcDpeIPKgAAAABJRU5ErkJggg==",gd=[{icon:J0,label:"节点样式",onClick:()=>{}},{icon:X0,label:"形状",onClick:()=>{}},{icon:$0,label:"节点背景",onClick:()=>{},addSeparator:!0},{icon:Z0,label:"连线类型",onClick:()=>{}},{icon:q0,label:"连线颜色",onClick:()=>{}},{icon:eA,label:"连线宽度",onClick:()=>{},addSeparator:!0},{icon:tA,label:"边框宽度",onClick:()=>{}},{icon:nA,label:"边框颜色",onClick:()=>{}},{icon:rA,label:"边框类型",onClick:()=>{},addSeparator:!0},{icon:oA,label:"清除样式",onClick:()=>{},addSeparator:!0},{icon:zp,label:"画布",onClick:()=>{}},{icon:Up,label:"风格",onClick:()=>{}},{icon:Qp,label:"结构",onClick:()=>{},addSeparator:!0},{icon:lA,label:"主题间距",onClick:()=>{}},{icon:iA,label:"主题宽度",onClick:()=>{}},{icon:Vp,label:"帮助",onClick:()=>{},iconOnly:!0}],vd=[{icon:sA,label:"查找替换",onClick:()=>{},addSeparator:!0,showDropdownArrow:!1},{icon:aA,label:"撤销（Ctrl+Z）",onClick:()=>{},iconOnly:!0},{icon:uA,label:"恢复（Ctrl+Y）",onClick:()=>{},iconOnly:!0},{icon:cA,label:"格式刷（Ctrl+Shift+C）",onClick:()=>{},addSeparator:!0,iconOnly:!0},{icon:"",label:"微软雅黑",onClick:()=>{},iconOnly:!1},{icon:"",label:"14px",onClick:()=>{},iconOnly:!1}],yd=[{icon:fA,label:"居左对齐",onClick:()=>{},iconOnly:!0},{icon:dA,label:"居中对齐",onClick:()=>{},iconOnly:!0},{icon:pA,label:"居右对齐",onClick:()=>{},addSeparator:!0,iconOnly:!0},{icon:hA,label:"子主题 (tab, insert)",onClick:()=>{},iconOnly:!0},{icon:Yl,label:"同级主题 (enter)",onClick:()=>{},iconOnly:!0},{icon:Kl,label:"父主题 (shift+tab)",onClick:()=>{},addSeparator:!0,iconOnly:!0},{icon:mA,label:"概要",onClick:()=>{},showDropdownArrow:!1},{icon:gA,label:"外框",onClick:()=>{},showDropdownArrow:!1},{icon:vA,label:"图片",onClick:()=>{},showDropdownArrow:!1},{icon:yA,label:"超链接",onClick:()=>{},showDropdownArrow:!1},{icon:AA,label:"水印",onClick:()=>{},addSeparator:!0},{icon:zp,label:"画布",onClick:()=>{}},{icon:Up,label:"风格",onClick:()=>{}},{icon:Qp,label:"结构",onClick:()=>{}},{icon:wA,label:"收起",onClick:()=>{}},{icon:xA,label:"展开",onClick:()=>{}},{icon:Vp,label:"帮助",onClick:()=>{},iconOnly:!0}],CA=[0,1,2,3,4,5],SA=({menuType:r})=>{const[l,s]=w.useState(!1),[u,c]=w.useState(!1),f=w.useRef(null),d=w.useRef(null),{selectedNode:h,mindMapData:m,toggleBold:g,toggleItalic:y,updateBorderWidth:A,updateTextColor:C}=ko(),S=h?m.nodes[h.id]:null,R=!!h,E=({icon:L,label:F,onClick:M,disabled:z=!1,size:U="sm",variant:J="ghost",showDropdownArrow:X=!0,iconOnly:oe=!1})=>v.jsxs(wr,{children:[v.jsx(xr,{asChild:!0,children:v.jsxs(Ye,{variant:J,size:U,disabled:z,className:"button-small",onClick:M,children:[L&&v.jsx("span",{className:"icon-small",children:L}),!oe&&v.jsx("span",{className:"text-small",children:F}),!oe&&X&&v.jsx("div",{className:"margin-left-medium",children:v.jsx(vo,{className:"dropdown-arrow"})})]})}),v.jsx(Cr,{children:F})," "]}),I=()=>v.jsx("div",{className:"menu-container",children:v.jsxs(wa,{delayDuration:0,children:[vd.map(({icon:L,label:F,onClick:M,showDropdownArrow:z,iconOnly:U},J)=>v.jsxs(Wn.Fragment,{children:[J>0&&vd[J-1].addSeparator&&v.jsx(Ar,{orientation:"vertical",className:"separator"}),v.jsx("div",{className:"button-container",children:v.jsx(E,{icon:L?v.jsx("img",{src:L,alt:F}):null,label:F,onClick:M,disabled:!R,showDropdownArrow:z,iconOnly:U})})]},F)),v.jsx(Ar,{orientation:"vertical",className:"separator"}),v.jsxs(wr,{children:[v.jsx(xr,{asChild:!0,children:v.jsx(Ye,{variant:"ghost",size:"sm",disabled:!R,onClick:()=>h&&g(h.id),className:`button-square ${(S==null?void 0:S.style.fontWeight)==="bold"?"button-active":""}`,children:v.jsx(Ed,{className:"icon-small"})})}),v.jsx(Cr,{children:"加粗（Crtl+B）"})]}),v.jsxs(wr,{children:[v.jsx(xr,{asChild:!0,children:v.jsx(Ye,{variant:"ghost",size:"sm",disabled:!R,onClick:()=>h&&y(h.id),className:`button-square ${(S==null?void 0:S.style.fontStyle)==="italic"?"button-active":""}`,children:v.jsx(Nd,{className:"icon-small"})})}),v.jsx(Cr,{children:"斜体（Crtl+I）"})]}),v.jsxs(ga,{open:u,onOpenChange:L=>{c(L),L||setTimeout(()=>{var F;(F=d.current)==null||F.blur()},100)},children:[v.jsxs(wr,{children:[v.jsx(xr,{asChild:!0,children:v.jsx(va,{asChild:!0,children:v.jsxs(Ye,{ref:d,variant:"ghost",size:"sm",disabled:!R,className:`button-square ${u?"button-active":""}`,children:[v.jsxs("svg",{className:"icon icon-small",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",children:[v.jsx("path",{d:"M825.6 652.8L544 83.2C537.6 70.4 524.8 64 512 64s-25.6 6.4-32 19.2l-281.6 569.6c-6.4 19.2 0 38.4 19.2 51.2 19.2 6.4 38.4 0 51.2-19.2L384 454.4h275.2l115.2 230.4c6.4 19.2 32 25.6 51.2 19.2 6.4-12.8 12.8-32 0-51.2zM409.6 384L512 172.8 614.4 384H409.6z",fill:"#2c2c2c","p-id":"9382"}),v.jsx("path",{d:"M876.8 960H147.2c-44.8 0-83.2-38.4-83.2-83.2v-19.2c0-51.2 38.4-89.6 83.2-89.6h723.2c44.8 0 83.2 38.4 83.2 83.2v19.2c6.4 51.2-32 89.6-76.8 89.6z","p-id":"9383","data-spm-anchor-id":"a313x.search_index.0.i19.4e3e3a81nzKYDC",fill:(S==null?void 0:S.style.color)||"#000000"})]})," ",v.jsx("div",{className:"margin-left-medium",children:u?v.jsx(Vf,{className:"dropdown-arrow"}):v.jsx(vo,{className:"dropdown-arrow"})})]})})}),v.jsx(Cr,{children:"字体颜色"})]}),v.jsx($l,{className:"popover-content-color",children:v.jsx(_p,{color:(S==null?void 0:S.style.color)||"#000000",onChange:L=>{h&&C(h.id,L)},onComplete:()=>{c(!1)}})})]}),v.jsx(Ar,{orientation:"vertical",className:"separator"}),yd.map(({icon:L,label:F,onClick:M,showDropdownArrow:z,iconOnly:U},J)=>v.jsxs(Wn.Fragment,{children:[J>0&&yd[J-1].addSeparator&&v.jsx(Ar,{orientation:"vertical",className:"separator"}),v.jsx("div",{className:"button-container",children:v.jsx(E,{icon:L?v.jsx("img",{src:L,alt:F}):null,label:F,onClick:M,disabled:!R,showDropdownArrow:z,iconOnly:U})})]},F))]})}),j=()=>v.jsx("div",{className:"menu-container",children:v.jsx(wa,{delayDuration:0,children:gd.map(({icon:L,label:F,onClick:M,showDropdownArrow:z,iconOnly:U},J)=>v.jsxs(Wn.Fragment,{children:[J>0&&gd[J-1].addSeparator&&v.jsx(Ar,{orientation:"vertical",className:"separator"}),v.jsx("div",{className:"button-container",children:F==="边框宽度"?v.jsx("div",{className:"flex-center-gap-small",children:v.jsxs(ga,{open:l,onOpenChange:X=>{s(X),X||setTimeout(()=>{var oe;(oe=f.current)==null||oe.blur()},100)},children:[v.jsxs(wr,{children:[v.jsx(va,{asChild:!0,children:v.jsx(xr,{asChild:!0,children:v.jsxs(Ye,{ref:f,variant:"ghost",size:"sm",disabled:!R,className:`button-small ${l?"button-active":""}`,children:[L&&v.jsx("span",{className:"icon-small",children:v.jsx("img",{src:L,alt:F})}),!U&&v.jsx("span",{className:"text-small",children:F}),v.jsx("div",{className:"margin-left-medium",children:l?v.jsx(Vf,{className:"dropdown-arrow"}):v.jsx(vo,{className:"dropdown-arrow"})})]})})}),v.jsx(Cr,{children:"边框宽度"})]}),v.jsx($l,{className:"popover-content-border",children:v.jsx("div",{className:"border-width-list",children:CA.map(X=>{const oe=(S==null?void 0:S.style.borderWidth)===X,me=X===0?"无":`${X}px`;return v.jsxs(Ye,{variant:"ghost",size:"sm",onClick:()=>{h&&A(h.id,X),s(!1)},className:`border-width-item ${oe?"border-width-selected":""}`,children:[v.jsx("svg",{className:`check-icon ${oe?"check-icon-visible":"check-icon-hidden"}`,width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:v.jsx("polyline",{points:"20,6 9,17 4,12"})}),v.jsx("span",{className:"border-width-text",children:me})]},X)})})})]})}):v.jsx(E,{icon:L?v.jsx("img",{src:L,alt:F}):null,label:F,onClick:M,disabled:!R,showDropdownArrow:z,iconOnly:U})})]},F))})}),B=()=>v.jsx("div",{className:"menu-container-wide",children:v.jsxs("span",{className:"text-small text-gray",children:[r==="insert"&&"插入功能",r==="view"&&"视图功能",r==="export"&&"导出功能"]})});return v.jsxs("div",{className:"secondary-menu",children:[r==="start"&&I(),r==="style"&&j(),(r==="insert"||r==="view"||r==="export")&&B()]})},EA=({node:r,isSelected:l,onSelect:s,onUpdate:u,onAddChild:c,onContextMenu:f,canAddChild:d,onToggleCollapse:h,onDimensionUpdate:m,onAdjustChildPosition:g})=>{const[y,A]=w.useState(!1),[C,S]=w.useState(r.text),[R,E]=w.useState(!1),[I,j]=w.useState(null),[B,L]=w.useState(null),[F,M]=w.useState(null),z=w.useRef(null),U=w.useRef(null),J=w.useRef(null),X=w.useRef(null),{formatToolbarNodeId:oe,setFormatToolbarNodeId:me,relayoutLevel2Nodes:xe,relayoutLevel3Nodes:ye,updateNode:Y,mindMapData:Z,setAiPromptText:le}=ko(),te=oe===r.id,_=(ie,ge)=>X.current?(X.current.style.fontWeight=ge.fontWeight,X.current.style.fontStyle=ge.fontStyle,X.current.style.fontSize=ge.fontSize,X.current.style.fontFamily=getComputedStyle(X.current).fontFamily,X.current.textContent=ie||"|",X.current.offsetWidth):null,b=w.useCallback((ie,ge,Me)=>{const Ge=Me-ge;if(Math.abs(Ge)<4)return;const Mt=Z.nodes[ie];if(!Mt||Mt.children.length===0)return;const rn=(Yn,pt)=>{const on=Z.nodes[Yn];on&&on.children.forEach(Ct=>{const ht=Z.nodes[Ct];ht&&(Mt.level===1?(ht.level===2||ht.level===3)&&Y(Ct,{position:{...ht.position,x:ht.position.x+pt}}):Mt.level===2&&ht.level===3&&Y(Ct,{position:{...ht.position,x:ht.position.x+pt}}),rn(Ct,pt))})};rn(ie,Ge)},[Z.nodes,Y]),G=ie=>{if(ie.stopPropagation(),J.current){const Me=J.current.querySelector(".node-text");if(Me){const Ge=Me.getBoundingClientRect();j(Ge.width),M(Ge.width)}}me(r.id),A(!0),S(r.text);const ge=_(r.text,we());ge&&L(ge)},P=w.useCallback(()=>{C.length>=1&&C.length<=100?(u({text:C}),C!==r.text&&J.current&&setTimeout(()=>{if(r.children.length>0&&(r.level===1?xe(r.id):r.level===2&&(ye(r.id),r.parentId&&setTimeout(()=>{xe(r.parentId)},50))),J.current&&m){const ge=J.current.getBoundingClientRect();m(r.id,{width:ge.width,height:ge.height},!0),r.children.length>0&&g&&setTimeout(()=>{g(r.id)},100)}setTimeout(()=>{var Ge;const ge=(Ge=J.current)==null?void 0:Ge.querySelector(".node-text");let Me=0;ge&&(Me=ge.getBoundingClientRect().width),F!==null&&r.children.length>0&&(r.level===1||r.level===2)&&b(r.id,F,Me)},200)},0)):S(r.text),A(!1),j(null),M(null),L(null)},[C,r,u,xe,ye,m,g,F,b]),Q=()=>{S(r.text),A(!1),j(null),M(null),L(null),me(null)},ue=ie=>{ie.key==="Enter"?(P(),me(null)):ie.key==="Escape"&&(Q(),me(null))},ce=(ie,ge)=>{u({style:{...r.style,[ie]:ge}})},he=()=>{ce("fontWeight",r.style.fontWeight==="bold"?"normal":"bold")},fe=()=>{ce("fontStyle",r.style.fontStyle==="italic"?"normal":"italic")},ve=ie=>{ce("color",ie)};w.useEffect(()=>{y&&z.current&&(z.current.focus(),z.current.select())},[y]),w.useEffect(()=>{if(J.current&&m){const ie=(Me=!1)=>{if(J.current){const Ge=J.current.getBoundingClientRect();m(r.id,{width:Ge.width,height:Ge.height},Me)}},ge=setTimeout(()=>ie(!1),0);return()=>{clearTimeout(ge)}}},[r.id,r.text,r.style,m,y]),w.useEffect(()=>{const ie=ge=>{te&&U.current&&ge.target instanceof Node&&(U.current.contains(ge.target)||(console.log(444),me(null),y&&P())),y&&J.current&&ge.target instanceof Node&&(J.current.contains(ge.target)||P())};if(te||y)return document.addEventListener("click",ie),()=>document.removeEventListener("click",ie)},[te,y,P,me]);const de=ie=>{ie.stopPropagation(),y||(oe!==r.id&&me(null),s())},we=()=>{const ie={fontWeight:r.style.fontWeight,fontStyle:r.style.fontStyle,color:r.style.color,borderWidth:`${r.style.borderWidth}px`,fontSize:"14px"};switch(r.level){case 1:return{...ie,backgroundColor:"rgb(0, 165, 154)",borderColor:"white",padding:"12px 16px",fontSize:"20px",fontWeight:r.style.fontWeight==="normal"?"400":"bold"};case 2:return{...ie,backgroundColor:"rgb(238, 238, 238)",borderColor:"#484848",padding:"10px 14px",fontSize:"14px"};case 3:return{...ie,backgroundColor:"rgb(238, 238, 238)",borderColor:"#484848",padding:"8px 12px",fontSize:"14px",whiteSpace:"nowrap"};default:return{...ie,fontSize:"14px"}}},Ee=we(),Ke=()=>{let ie="mindmap-node";switch(l&&(ie+=" selected"),y&&(ie+=" editing"),r.level){case 1:ie+=" level-1";break;case 2:ie+=" level-2";break;case 3:ie+=" level-3";break}return ie};return v.jsxs("div",{className:"mindmap-node-container",style:{left:r.position.x,top:r.position.y},children:[v.jsx("span",{ref:X,style:{position:"absolute",visibility:"hidden",whiteSpace:"nowrap",pointerEvents:"none"}}),v.jsxs("div",{ref:J,className:Ke(),style:Ee,onClick:de,onDoubleClick:G,onContextMenu:f,children:[y?v.jsx("input",{ref:z,type:"text",value:C,onChange:ie=>{const ge=ie.target.value;S(ge),me(null),le(ge);const Me=_(ge,Ee);Me&&L(Me)},onBlur:P,onKeyDown:ue,maxLength:100,className:"node-input",placeholder:"输入节点内容","aria-label":"编辑节点内容",style:{fontWeight:Ee.fontWeight,fontStyle:Ee.fontStyle,color:Ee.color,fontSize:Ee.fontSize,width:B?`${B}px`:I?`${I}px`:"auto"}}):v.jsx("span",{className:"node-text",children:r.text}),r.level===2&&r.children.length>0&&v.jsx(Ye,{size:"sm",variant:"ghost",className:"btn btn-outline btn-sm add-child-button",onClick:ie=>{ie.stopPropagation(),h==null||h()},children:r.collapsed?v.jsx(kd,{className:"w-3 h-3"}):v.jsx(vo,{className:"w-3 h-3"})}),l&&d&&!y&&r.children.length===0&&v.jsx(Ye,{size:"sm",variant:"outline",className:"add-child-button",onClick:ie=>{ie.stopPropagation(),c()},children:v.jsx(ug,{className:"add-icon"})})]}),y&&v.jsxs("div",{className:"char-count",children:[C.length,"/100",C.length===0&&v.jsx("span",{className:"error",children:"至少1个字符"}),C.length>100&&v.jsx("span",{className:"error",children:"超出最大长度"})]}),te&&v.jsx("div",{className:"format-toolbar",ref:U,children:v.jsx(wa,{delayDuration:0,children:v.jsxs("div",{className:"toolbar-content",children:[v.jsx(Ye,{size:"sm",variant:r.style.fontWeight==="bold"?"default":"ghost",className:`toolbar-button ${r.style.fontWeight==="bold"?"button-active":""}`,onClick:ie=>{ie.stopPropagation(),he()},children:v.jsx(Ed,{className:"toolbar-icon"})}),v.jsx(Ye,{size:"sm",variant:r.style.fontStyle==="italic"?"default":"ghost",className:`toolbar-button ${r.style.fontStyle==="italic"?"button-active":""}`,onClick:ie=>{ie.stopPropagation(),fe()},children:v.jsx(Nd,{className:"toolbar-icon"})}),v.jsx(Ye,{size:"sm",variant:"ghost",className:"toolbar-button",disabled:!0,children:v.jsx(pg,{className:"toolbar-icon"})}),v.jsx(Ye,{size:"sm",variant:"ghost",className:"toolbar-button",disabled:!0,children:v.jsx(fg,{className:"toolbar-icon"})}),v.jsxs(ga,{open:R,onOpenChange:E,children:[v.jsx(wr,{children:v.jsx(xr,{asChild:!0,children:v.jsx(va,{asChild:!0,children:v.jsx(Ye,{variant:"ghost",size:"sm",className:`toolbar-button ${R?"button-active":""}`,children:v.jsxs("svg",{className:"toolbar-icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",children:[v.jsx("path",{d:"M825.6 652.8L544 83.2C537.6 70.4 524.8 64 512 64s-25.6 6.4-32 19.2l-281.6 569.6c-6.4 19.2 0 38.4 19.2 51.2 19.2 6.4 38.4 0 51.2-19.2L384 454.4h275.2l115.2 230.4c6.4 19.2 32 25.6 51.2 19.2 6.4-12.8 12.8-32 0-51.2zM409.6 384L512 172.8 614.4 384H409.6z",fill:"#2c2c2c"}),v.jsx("path",{d:"M876.8 960H147.2c-44.8 0-83.2-38.4-83.2-83.2v-19.2c0-51.2 38.4-89.6 83.2-89.6h723.2c44.8 0 83.2 38.4 83.2 83.2v19.2c6.4 51.2-32 89.6-76.8 89.6z",fill:r.style.color||"#000000"})]})})})})}),v.jsx($l,{className:"popover-content-color",children:v.jsx(_p,{color:r.style.color||"#000000",onChange:ie=>{ve(ie)},onComplete:()=>{E(!1)}})})]}),v.jsx(Ye,{size:"sm",variant:"ghost",className:"toolbar-button",disabled:!0,children:v.jsxs("svg",{className:"toolbar-icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",children:[v.jsx("path",{d:"M512 928H128a32 32 0 0 1-26.88-49.92L345.6 512 101.12 145.92A32 32 0 0 1 128 96h384a32 32 0 0 1 0 64H187.52l223.36 334.08a33.28 33.28 0 0 1 0 35.84L187.52 864H512a32 32 0 0 1 0 64zM640 928a36.48 36.48 0 0 1-17.92-5.12 32.64 32.64 0 0 1-8.96-44.8l256-384a32 32 0 0 1 53.76 35.84l-256 384a33.28 33.28 0 0 1-26.88 14.08z",fill:"#4D4D4D"}),v.jsx("path",{d:"M896 928a33.28 33.28 0 0 1-26.88-14.08l-256-384a32 32 0 1 1 53.76-35.84l256 384a32.64 32.64 0 0 1-8.96 44.8 36.48 36.48 0 0 1-17.92 5.12z",fill:"#4D4D4D"})]})}),v.jsx(Ye,{size:"sm",variant:"ghost",className:"toolbar-button",disabled:!0,children:v.jsx(sg,{className:"toolbar-icon"})}),v.jsx(Ye,{size:"sm",variant:"ghost",className:"toolbar-button",disabled:!0,children:v.jsx(lg,{className:"toolbar-icon"})})]})})})]})};function kA({mindMapData:r,selectedNode:l,onNodeSelect:s,onNodeUpdate:u,onAddChild:c,onToggleCollapse:f,onContextMenu:d}){const h=w.useRef(null),[m,g]=w.useState({}),y=w.useRef(!1),[A,C]=w.useState(!1),[S,R]=w.useState({x:0,y:0}),[E,I]=w.useState({x:0,y:0}),j=w.useRef({x:0,y:0}),B=w.useCallback((Y,Z,le)=>{if(y.current)return;const te=r.nodes[Y];if(!te||te.children.length===0)return;let _=0;if(Z!==void 0&&le!==void 0&&(_=le-Z,Math.abs(_)<1))return;if(y.current=!0,!m[Y]){y.current=!1;return}const G=(P,Q)=>{const ue=r.nodes[P];ue&&ue.children.forEach(ce=>{var fe;const he=r.nodes[ce];if(he){const ve=ue.position.x+(((fe=m[P])==null?void 0:fe.width)||0),de=Q!==0?ve+Q:ve;u(ce,{position:{...he.position,x:de}}),G(ce,Q)}})};G(Y,_),setTimeout(()=>{y.current=!1},100)},[r.nodes,u,m]),L=w.useCallback((Y,Z,le=!1)=>{g(te=>{const _=te[Y],b={...te,[Y]:Z};if(!y.current&&le&&_&&Math.abs(_.width-Z.width)>1){const G=r.nodes[Y];G&&G.children.length>0&&setTimeout(()=>{B(Y,_.width,Z.width)},0)}return b})},[r.nodes,B]),F=w.useCallback(Y=>{c(Y),setTimeout(()=>{B(Y)},50)},[c,B]),M=w.useCallback(Y=>{B(Y)},[B]),z=w.useCallback((Y,Z)=>{const le=fe=>{const ve=m[fe.id];if(ve)return ve;const de=fe.style.borderWidth||1,we=fe.text.length;switch(fe.level){case 1:{const Ee=de*2+32+we*20,Ke=de*2+24+20;return{width:Ee,height:Ke}}case 2:{const Ee=de*2+28+we*14,Ke=de*2+20+14;return{width:Ee,height:Ke}}case 3:{const Ee=de*2+24+we*14,Ke=de*2+16+14;return{width:Ee,height:Ke}}default:{const ge=de*2+28+we*14,Me=de*2+20+14;return{width:ge,height:Me}}}},te=le(Y),_=le(Z),b=Y.position.x+te.width,G=Y.position.y+te.height/2,P=Z.position.x,Q=Z.position.y+_.height/2,ue=Y.children.length,ce=Y.children.indexOf(Z.id);let he=!1;if(Y.level===1)he=!1;else if(Y.level===2)if(ue%2===1){const fe=Math.floor(ue/2);he=ce===fe}else he=!1;else he=!1;if(he)return`M ${b} ${G} L ${P} ${Q}`;{const fe=b+(P-b)*.3,ve=G,de=b+(P-b)*.7;return`M ${b} ${G} C ${fe} ${ve}, ${de} ${Q}, ${P} ${Q}`}},[m]),U=()=>{const Y=[];for(const Z of Object.values(r.nodes))if(!Z.collapsed)for(const le of Z.children){const te=r.nodes[le];if(te){const _=z(Z,te);Y.push(v.jsx("path",{d:_,stroke:"#666",strokeWidth:"2",fill:"none",className:"connection-line"},`${Z.id}-${le}`))}}return Y},X=(()=>{const Y=Object.values(r.nodes);if(Y.length===0)return{width:1e3,height:800,minX:0,minY:0};let Z=Number.POSITIVE_INFINITY,le=Number.POSITIVE_INFINITY,te=Number.NEGATIVE_INFINITY,_=Number.NEGATIVE_INFINITY;for(const b of Y)Z=Math.min(Z,b.position.x),le=Math.min(le,b.position.y),te=Math.max(te,b.position.x+200),_=Math.max(_,b.position.y+40);return{width:Math.max(1e3,te-Z+500),height:Math.max(800,_-le+500),minX:Math.min(0,Z-100),minY:Math.min(0,le-100)}})(),oe=Y=>{const Z=Y.target;Z.tagName==="INPUT"||Z.tagName==="BUTTON"||Z.closest("button")||Z.closest(".color-picker")||Z.closest(".format-toolbar")||(C(!0),R({x:Y.clientX,y:Y.clientY}),j.current={...E},Y.preventDefault())},me=w.useCallback(Y=>{if(A){const Z=Y.clientX-S.x,le=Y.clientY-S.y;I({x:j.current.x+Z,y:j.current.y+le})}},[A,S.x,S.y]),xe=w.useCallback(()=>{C(!1)},[]);w.useEffect(()=>{if(A)return document.addEventListener("mousemove",me),document.addEventListener("mouseup",xe),()=>{document.removeEventListener("mousemove",me),document.removeEventListener("mouseup",xe)}},[A,me,xe]);const ye=Y=>{Y.target===Y.currentTarget&&s(null)};return v.jsxs("div",{ref:h,className:"mindmap-canvas",onClick:ye,onMouseDown:oe,style:{cursor:A?"grabbing":"grab"},children:[v.jsx("svg",{className:"connections-layer",width:"100%",height:"100%",style:{transform:`translate(${-X.minX+E.x}px, ${-X.minY+E.y}px)`},children:U()}),v.jsx("div",{className:"nodes-layer",style:{width:X.width,height:X.height,transform:`translate(${-X.minX+E.x}px, ${-X.minY+E.y}px)`},children:Object.values(r.nodes).filter(Y=>{if(Y.level===1)return!0;if(Y.parentId){const Z=r.nodes[Y.parentId];return!(Z!=null&&Z.collapsed)}return!0}).map(Y=>v.jsx(EA,{node:Y,isSelected:(l==null?void 0:l.id)===Y.id,onSelect:()=>s({id:Y.id,level:Y.level}),onUpdate:Z=>u(Y.id,Z),onAddChild:()=>F(Y.id),onToggleCollapse:()=>f(Y.id),onContextMenu:Z=>d(Z,Y.id,Y.level),canAddChild:Y.level<3,onDimensionUpdate:L,onAdjustChildPosition:M},Y.id))})]})}const ca="data:image/png;base64,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",NA="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAaCAIAAADJ6rCnAAAAAXNSR0IArs4c6QAAAnNJREFUSA2NlbGL+jAUx/1n8k9kK9zgdlNH5YZuwsEJhzh2qyBOEm4oN5Rbjm4tCF2uNxxxjBzUSZfeIGRwKAgBh/xo0tYkrT/tUJO+9z7vm5fk2eM3n2PqQgCgmx5vupYOvdteGwTEgza3fe8gnlnxvZDExXfBzrehVzUW2wS99KGEXd6w/4KSbfEfcBfxtAsn1gXSNbIm4e7UjW0R/1LXrhkPjhskZE8L8dA9SQLXeaittpv+dUB14omgCgedN0w7q3am+M2pqmEj0lKqEhmeSU84jvOO7MqnPB5XrjPMlO/6Xm99qc9eEsNJD5EzRpaVu7/V7I1GhuciK/RwayFaRDM5YU9GzDWZDTFD0rwkTUg1oCQKfD+ICDUtZCljUKZYepxmeI3xajEUe+j96Cs+JFXBAABwnByUUM7ZjyeChosVxmuciZQ9Ka0+EcC4ajQeNSYAwCjWddYXtPKBpdjex0Aor+MMYvHl1pby1/3Sb4tOhIOPnegUrDy/+1CKGa90FSzz65Rw4Gd6SehqLPKNwn3JkMZmZ/LwSVhb54tzRrcYb3JdHuec4ZkIeQrV09sQefb+KBNGevm1vVAnh0gu6/Fd3Wq1Px6TqUDC10jNqUKUcR69yg2YJnonvmjknOefjmACe45ba1RovMDz6v47n2Z2jch5kxlYzz4+6BshmOyA/eeq13WuxiCWQhOlOVr21AuidI3xOo2Chetc+qY1SUx5ImWbyDkvdrE31I6pLEb9hkMv3l0rSydRLo+SGE0H6h8D7A+mKCa0oxiXKl8nNj71xTCuU2M3BncQf6uuhH6N2O7pHUSW+Y5lOeYV7OZx/g/9cng3ASkb5AAAAABJRU5ErkJggg==",Ad=[{icon:"",label:"插入",describe:"",onClick:()=>{}},{icon:"",label:"编号",describe:"",onClick:()=>{},addMenuSeparator:!0},{icon:"",label:"收起主题",describe:"",onClick:()=>{}},{icon:"",label:"选择主题",describe:"",onClick:()=>{},addMenuSeparator:!0},{icon:"",label:"复制",describe:"Ctrl + C",onClick:()=>{}},{icon:"",label:"剪切",describe:"Ctrl + X",onClick:()=>{}},{icon:"",label:"粘贴主题",describe:"Ctrl + V",onClick:()=>{}},{icon:"",label:"删除",describe:"Del",onClick:()=>{}},{icon:"",label:"删除当前主题",describe:"Ctrl + Del",onClick:()=>{}},{icon:"",label:"批量删除",describe:"",onClick:()=>{},addMenuSeparator:!0},{icon:"",label:"聚焦模式",describe:"Ctrl + `",onClick:()=>{}},{icon:NA,label:"导出/复制当前主题",describe:" ",onClick:()=>{}}],PA="/assets/ai-B79k_uiS.png",RA=async(r,l)=>{var s,u,c,f,d,h;if(console.log("开始调用AI生成接口...",{prompt:r}),!r||r.trim()==="")throw new Error("prompt不能为空");try{const m=await fetch("/api/chatStream",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:r.trim()})});if(!m.ok)throw new Error(`HTTP error! status: ${m.status}`);if(!m.body)throw new Error("Response body is null");console.log("开始接收SSE流式响应...");const g=m.body.getReader(),y=new TextDecoder;let A="",C="";for(;;){const{done:S,value:R}=await g.read();if(S){console.log("流式响应结束");break}A+=y.decode(R,{stream:!0});const E=A.split(`

`);A=E.pop()||"";for(const I of E){if(I.trim()==="")continue;const j=I.match(/^event: (.+)$/m),B=I.match(/^data: (.+)$/m);if(j&&B){const L=j[1],F=B[1];switch(console.log(`事件类型: ${L}, 数据:`,F),L){case"start":console.log("AI开始生成响应..."),(s=l==null?void 0:l.onStart)==null||s.call(l);break;case"end":console.log("AI响应完成"),console.log("完整响应内容:",C),(u=l==null?void 0:l.onEnd)==null||u.call(l,C);break;case"error":console.error("AI响应错误:",F);let M="AI响应错误";try{const z=JSON.parse(F);M=z.message||z.error||M}catch{M=F||M}(c=l==null?void 0:l.onError)==null||c.call(l,M);break}}else if(B)try{const L=JSON.parse(B[1]);L.text&&(console.log("接收到文本片段:",L.text),C+=L.text,(f=l==null?void 0:l.onData)==null||f.call(l,L.text))}catch(L){console.warn("解析数据错误:",L,B[1])}}}return console.log(`
=== AI生成完成 ===`),console.log("最终完整响应:",C),C}catch(m){if(console.error("调用AI接口时发生错误:",m),m instanceof TypeError&&m.message.includes("fetch")){const g="网络连接错误，请检查服务器是否正在运行";throw console.error(g),(d=l==null?void 0:l.onError)==null||d.call(l,g),new Error(g)}throw(h=l==null?void 0:l.onError)==null||h.call(l,m instanceof Error?m.message:"未知错误"),m}},IA=async(r,l,s)=>{var d;if(console.log("🧠 开始AI思维导图节点生成...",{prompt:r,parentNodeId:l}),!r||r.trim()==="")throw new Error("prompt不能为空");const u=[];let c="";const f={1:l};try{return await RA(r,{onStart:()=>{var h;console.log("🤖 AI开始生成思维导图节点..."),(h=s==null?void 0:s.onStart)==null||h.call(s)},onData:h=>{var g;console.log("📝 接收到数据片段:",h),c+=h;const m=c.split("STOP");c=m.pop()||"";for(const y of m){const A=y.trim();if(!(A===""||A==="END"))try{const C=JSON.parse(A);if(C.id&&C.text&&C.level){if(C.level===1){console.log("⏭️ 跳过level 1节点:",C);continue}let S=C.parentId||l;C.parentId&&f[C.parentId]&&(S=f[C.parentId],console.log(`🔗 ID映射: AI的"${C.parentId}" -> 实际的"${S}"`));const R=`ai-${Date.now()}-${Math.random().toString(36).substring(2,11)}`;f[C.id]=R;const E={id:R,text:C.text,level:C.level,parentId:S};console.log("✅ 解析到完整节点:",E),console.log("🗂️ 当前ID映射表:",f),u.push(E),(g=s==null?void 0:s.onNodeGenerated)==null||g.call(s,E)}}catch{console.log("⏳ JSON解析失败，等待更多数据:",A)}}},onEnd:h=>{var m,g;if(console.log("🎉 AI思维导图节点生成完成"),console.log("📊 生成的节点数量:",u.length),console.log("📋 所有节点:",u),c.trim()){const y=c.trim().replace(/END$/,"").trim();if(y)try{const A=JSON.parse(y);if(A.id&&A.text&&A.level&&A.level!==1){let C=A.parentId||l;A.parentId&&f[A.parentId]&&(C=f[A.parentId]);const R={id:`ai-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,text:A.text,level:A.level,parentId:C};console.log("✅ 解析到最后一个节点:",R),u.push(R),(m=s==null?void 0:s.onNodeGenerated)==null||m.call(s,R)}}catch{console.warn("⚠️ 最后一段数据解析失败:",y)}}(g=s==null?void 0:s.onEnd)==null||g.call(s,u)},onError:h=>{var m;console.error("❌ AI思维导图节点生成错误:",h),(m=s==null?void 0:s.onError)==null||m.call(s,h)}}),u}catch(h){throw console.error("💥 AI思维导图节点生成失败:",h),(d=s==null?void 0:s.onError)==null||d.call(s,h instanceof Error?h.message:"未知错误"),h}},DA=async({nodeId:r,aiPromptText:l,mindMapData:s,clearNodeChildren:u,addAiGeneratedNode:c,setAiGenerating:f,onClose:d})=>{console.log("🤖 点击AI创作按钮，开始调用AI接口..."),d();const h=s.nodes[r];if(!h){console.error("❌ 没有找到当前节点");return}console.log(" 调试信息:"),console.log("  - aiPromptText:",`"${l}"`),console.log("  - currentNode.text:",`"${h.text}"`),console.log("  - nodeId:",r);const m=l.trim();if(console.log("  prompt:",`"${m}"`),!m){console.warn("⚠️ 请先在节点中输入内容作为AI生成的主题"),alert("请先在节点中输入内容作为AI生成的主题");return}if(!r){console.error("❌ 没有选中的节点");return}h&&h.children.length>0&&(console.log(`🧹 检测到节点有 ${h.children.length} 个子节点，开始清理...`),u(r),console.log("✅ 子节点清理完成，开始AI创作")),console.log("📝 使用的prompt:",m),console.log("🎯 目标父节点ID:",r);try{f(!0),await IA(m,r,{onStart:()=>{console.log("🚀 开始AI思维导图生成...")},onNodeGenerated:g=>{console.log("📦 生成新节点:",g),c(g)},onEnd:g=>{console.log("✅ AI思维导图生成完成，共生成",g.length,"个节点");const{relayoutLevel2Nodes:y,relayoutLevel3Nodes:A,mindMapData:C}=ko.getState(),S=C.nodes[r];S&&(S.level===1?setTimeout(()=>{y(r)},100):S.level===2&&setTimeout(()=>{A(r),S.parentId&&setTimeout(()=>{y(S.parentId)},50)},100)),f(!1)},onError:g=>{console.error("❌ AI生成错误:",g),f(!1),alert(`AI生成失败: ${g}`)}})}catch(g){console.error("❌ AI创作失败:",g),f(!1),alert(`AI创作失败: ${g instanceof Error?g.message:"未知错误"}`)}},Dt=({icon:r,label:l,describe:s,onClick:u,disabled:c=!1})=>v.jsxs("button",{type:"button",className:"menu-item",onClick:c?void 0:u,disabled:c,children:[v.jsx("span",{className:"menu-item-icon",children:r}),v.jsx("span",{className:"menu-item-label",children:l}),s!==void 0&&v.jsx("div",{className:"menu-item-describe",children:s===""?v.jsx(kd,{className:"icon-small"}):s})]}),Ql=()=>v.jsx("div",{className:"menu-separator"}),OA=({isOpen:r,position:l,nodeId:s,nodeLevel:u,onClose:c,onAddChild:f,onAddSibling:d,onDeleteNode:h})=>{const m=w.useRef(null),{aiPromptText:g,addAiGeneratedNode:y,setAiGenerating:A,clearNodeChildren:C,mindMapData:S}=ko(),R=async()=>{await DA({nodeId:s,aiPromptText:g,mindMapData:S,clearNodeChildren:C,addAiGeneratedNode:y,setAiGenerating:A,onClose:c})};w.useEffect(()=>{const M=z=>{m.current&&!m.current.contains(z.target)&&c()};return r&&document.addEventListener("mousedown",M),()=>{document.removeEventListener("mousedown",M)}},[r,c]);const E=()=>{const U=window.innerWidth,J=window.innerHeight;let X=l.x,oe=l.y;return X+180>U&&(X=U-180-10),oe+200>J&&(oe=J-200-10),X<0&&(X=10),oe<0&&(oe=10),{x:X,y:oe}};if(!r||!s||!u)return null;const I=E(),j=()=>{f(s),c()},B=()=>{d(s),c()},L=()=>{h(s),c()},F=()=>{const M=()=>Ad.map(({icon:z,label:U,describe:J,onClick:X},oe)=>v.jsxs(Wn.Fragment,{children:[oe>0&&Ad[oe-1].addMenuSeparator&&v.jsx(Ql,{}),v.jsx(Dt,{icon:z?v.jsx("img",{src:z,alt:U,className:"icon-small"}):null,label:U,describe:J,onClick:()=>{u!==1&&U==="删除"?L():X(),c()},disabled:u===1?["收起主题","选择主题","剪切","删除","删除当前主题","聚焦模式"].includes(U):u===3?["收起主题","删除当前主题"].includes(U):!1})]},`${U}-${oe}`));switch(u){case 1:return v.jsxs(v.Fragment,{children:[v.jsx(Dt,{icon:v.jsx("img",{src:PA,alt:"AI创作",className:"icon-small"}),label:"AI创作",describe:"",onClick:R}),v.jsx(Dt,{icon:v.jsx("img",{src:ca,alt:"新增子主题",className:"icon-small"}),label:"新增子主题",describe:"Tab",onClick:j}),v.jsx(Dt,{icon:v.jsx("img",{src:Yl,alt:"新增同级主题",className:"icon-small"}),label:"新增同级主题",describe:"Enter",onClick:()=>c(),disabled:!0}),v.jsx(Dt,{icon:v.jsx("img",{src:Kl,alt:"新增父主题",className:"icon-small"}),label:"新增父主题",describe:"Shift + Tab",onClick:()=>c(),disabled:!0}),v.jsx(Ql,{}),M()]});case 2:return v.jsxs(v.Fragment,{children:[v.jsx(Dt,{icon:v.jsx("img",{src:ca,alt:"新增子主题",className:"icon-small"}),label:"新增子主题",describe:"Tab",onClick:j}),v.jsx(Dt,{icon:v.jsx("img",{src:Yl,alt:"新增同级主题",className:"icon-small"}),label:"新增同级主题",describe:"Enter",onClick:B}),v.jsx(Dt,{icon:v.jsx("img",{src:Kl,alt:"新增父主题",className:"icon-small"}),label:"新增父主题",describe:"Shift + Tab",onClick:()=>c(),disabled:!0}),v.jsx(Ql,{}),M()]});case 3:return v.jsxs(v.Fragment,{children:[v.jsx(Dt,{icon:v.jsx("img",{src:ca,alt:"新增子主题",className:"icon-small"}),label:"新增子主题",describe:"Tab",onClick:()=>c(),disabled:!0}),v.jsx(Dt,{icon:v.jsx("img",{src:Yl,alt:"新增同级主题",className:"icon-small"}),label:"新增同级主题",describe:"Enter",onClick:B}),v.jsx(Dt,{icon:v.jsx("img",{src:Kl,alt:"新增父主题",className:"icon-small"}),label:"新增父主题",describe:"Shift + Tab",onClick:()=>c(),disabled:!0}),v.jsx(Ql,{}),M()]});default:return null}};return v.jsx("div",{ref:m,className:"context-menu",style:{left:I.x,bottom:"5px"},children:F()})},TA=({isVisible:r})=>r?v.jsx("div",{className:"ai-loading-indicator",children:v.jsxs("div",{className:"ai-loading-content",children:[v.jsxs("div",{className:"ai-loading-spinner",children:[v.jsx("div",{className:"spinner-ring"}),v.jsx("div",{className:"spinner-ring"}),v.jsx("div",{className:"spinner-ring"})]}),v.jsx("div",{className:"ai-loading-text",children:v.jsx("div",{className:"ai-loading-title",children:"🤖 AI正在创作中..."})})]})}):null,MA=()=>{const{mindMapData:r,selectedNode:l,activeMenu:s,contextMenu:u,isAiGenerating:c,setActiveMenu:f,setSelectedNode:d,setContextMenu:h,updateNode:m,addChildNode:g,addSiblingNode:y,deleteNode:A,toggleNodeCollapse:C}=ko();w.useEffect(()=>{const E=I=>{I.key==="Delete"&&l&&A(l.id)};return window.addEventListener("keydown",E),()=>window.removeEventListener("keydown",E)},[l,A]);const S=()=>{h({...u,isOpen:!1})},R=(E,I,j)=>{E.preventDefault(),h({isOpen:!0,position:{x:E.clientX,y:E.clientY},nodeId:I,nodeLevel:j})};return v.jsxs("div",{className:"app-container",children:[v.jsx(Jm,{activeMenu:s,onMenuChange:f}),s&&v.jsx(SA,{menuType:s}),v.jsxs("div",{className:"canvas-container",onClick:S,children:[v.jsx(TA,{isVisible:c}),v.jsx(kA,{mindMapData:r,selectedNode:l,onNodeSelect:d,onNodeUpdate:m,onAddChild:g,onToggleCollapse:C,onContextMenu:R})]}),u.isOpen&&v.jsx(OA,{isOpen:u.isOpen,position:u.position,nodeId:u.nodeId,nodeLevel:u.nodeLevel,onClose:S,onAddChild:g,onAddSibling:y,onDeleteNode:A})]})};Gm.createRoot(document.getElementById("root")).render(v.jsx(w.StrictMode,{children:v.jsx(MA,{})}));
