/* Popover 组件样式 */

.popover-content {
  z-index: 50;
  width: 288px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background-color: white;
  padding: 16px;
  color: #0f172a;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  outline: none;
  transform-origin: var(--radix-popover-content-transform-origin);
}

.popover-content[data-state="open"] {
  animation: popover-in 0.15s ease-out;
}

.popover-content[data-state="closed"] {
  animation: popover-out 0.15s ease-in;
}

/* 动画定义 */
@keyframes popover-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes popover-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* 滑入动画 */
.popover-content[data-side="bottom"] {
  animation: popover-in 0.15s ease-out, slide-in-from-top 0.15s ease-out;
}

.popover-content[data-side="top"] {
  animation: popover-in 0.15s ease-out, slide-in-from-bottom 0.15s ease-out;
}

.popover-content[data-side="left"] {
  animation: popover-in 0.15s ease-out, slide-in-from-right 0.15s ease-out;
}

.popover-content[data-side="right"] {
  animation: popover-in 0.15s ease-out, slide-in-from-left 0.15s ease-out;
}

@keyframes slide-in-from-top {
  from {
    transform: translateY(-8px);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom {
  from {
    transform: translateY(8px);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-from-left {
  from {
    transform: translateX(-8px);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slide-in-from-right {
  from {
    transform: translateX(8px);
  }
  to {
    transform: translateX(0);
  }
}
