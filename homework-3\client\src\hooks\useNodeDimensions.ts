import { useState, useCallback } from "react";
import type { MindMapNode as NodeType } from "../types/mindmap";

interface NodeDimensions {
  width: number;
  height: number;
}

interface UseNodeDimensionsReturn {
  nodeDimensions: Record<string, NodeDimensions>;
  updateNodeDimensions: (
    nodeId: string,
    dimensions: NodeDimensions,
    isFromEditing?: boolean
  ) => void;
  getNodeDimensions: (node: NodeType) => NodeDimensions;
}

export function useNodeDimensions(): UseNodeDimensionsReturn {
  const [nodeDimensions, setNodeDimensions] = useState<
    Record<string, NodeDimensions>
  >({});

  // 计算节点尺寸（基于文本字符数和样式）
  const calculateNodeDimensions = useCallback((node: NodeType): NodeDimensions => {
    const borderWidth = node.style.borderWidth || 1;
    const textLength = node.text.length;

    switch (node.level) {
      case 1: {
        // padding: 12px 16px, fontSize: 20px
        const level1Width = borderWidth * 2 + 16 * 2 + textLength * 20;
        const level1Height = borderWidth * 2 + 12 * 2 + 20;
        return { width: level1Width, height: level1Height };
      }

      case 2: {
        // padding: 10px 14px, fontSize: 14px
        const level2Width = borderWidth * 2 + 14 * 2 + textLength * 14;
        const level2Height = borderWidth * 2 + 10 * 2 + 14;
        return { width: level2Width, height: level2Height };
      }

      case 3: {
        // padding: 8px 12px, fontSize: 14px
        const level3Width = borderWidth * 2 + 12 * 2 + textLength * 14;
        const level3Height = borderWidth * 2 + 8 * 2 + 14;
        return { width: level3Width, height: level3Height };
      }

      default: {
        const defaultPaddingH = 14;
        const defaultPaddingV = 10;
        const defaultFontSize = 14;

        const defaultWidth =
          borderWidth * 2 +
          defaultPaddingH * 2 +
          textLength * defaultFontSize;
        const defaultHeight =
          borderWidth * 2 + defaultPaddingV * 2 + defaultFontSize;
        return { width: defaultWidth, height: defaultHeight };
      }
    }
  }, []);

  // 获取节点尺寸（优先使用实际尺寸，否则基于文本字符数和样式计算）
  const getNodeDimensions = useCallback(
    (node: NodeType): NodeDimensions => {
      // 优先使用存储的实际尺寸
      const actualDimensions = nodeDimensions[node.id];
      if (actualDimensions) {
        return actualDimensions;
      }

      // 如果没有实际尺寸，则动态计算
      return calculateNodeDimensions(node);
    },
    [nodeDimensions, calculateNodeDimensions]
  );

  // 更新节点尺寸
  const updateNodeDimensions = useCallback(
    (
      nodeId: string,
      dimensions: NodeDimensions,
      isFromEditing: boolean = false
    ) => {
      setNodeDimensions((prev) => {
        const prevDimensions = prev[nodeId];
        const newDimensions = {
          ...prev,
          [nodeId]: dimensions,
        };

        return newDimensions;
      });
    },
    []
  );

  return {
    nodeDimensions,
    updateNodeDimensions,
    getNodeDimensions,
  };
} 