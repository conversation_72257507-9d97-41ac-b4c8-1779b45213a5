import React, { useRef } from "react";
import { Bold, Italic, ChevronUp, ChevronDown } from "lucide-react";
import { Button } from "../common/button";
import { Separator } from "../common/separator";
import { Popover, PopoverContent, PopoverTrigger } from "../common/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../common/tooltip";
import { useMindMapStore } from "../../store/mindMapStore";
import CustomColorPicker from "../common/CustomColorPicker";
import { TooltipButton } from "./TooltipButton";
import { start_t_icons, start_b_icons } from "../../config/constants";

interface StartMenuProps {
  isNodeSelected: boolean;
  currentNode: any;
}

export const StartMenu = ({ isNodeSelected, currentNode }: StartMenuProps) => {
  const colorButtonRef = useRef<HTMLButtonElement>(null);
  const [colorPickerOpen, setColorPickerOpen] = React.useState(false);

  // 使用全局状态管理
  const { selectedNode, toggleBold, toggleItalic, updateTextColor } =
    useMindMapStore();

  return (
    <div className="menu-container">
      <TooltipProvider delayDuration={0}>
        {start_t_icons.map(
          ({ icon, label, onClick, showDropdownArrow, iconOnly }, index) => (
            <React.Fragment key={label}>
              {/* 如果前一个图标有 addSeparator 属性，则在当前图标前显示分隔符 */}
              {index > 0 && start_t_icons[index - 1].addSeparator && (
                <Separator orientation="vertical" className="separator" />
              )}
              <div className="button-container">
                <TooltipButton
                  icon={icon ? <img src={icon} alt={label} /> : null} // 只有当icon不为空时才显示图标
                  label={label} // 文本
                  onClick={onClick} // 传入按钮点击时调用的函数
                  disabled={!isNodeSelected} // 如果没有选中节点，按钮禁用
                  showDropdownArrow={showDropdownArrow} // 是否显示下拉箭头
                  iconOnly={iconOnly} // 是否只显示图标
                />
              </div>
            </React.Fragment>
          )
        )}
        <Separator orientation="vertical" className="separator" />
        {/* 加粗按钮 */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              disabled={!isNodeSelected}
              onClick={() => selectedNode && toggleBold(selectedNode.id)}
              className={`button-square ${currentNode?.style.fontWeight === "bold" ? "button-active" : ""}`}
            >
              <Bold className="icon-small" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>加粗（Crtl+B）</TooltipContent>
        </Tooltip>

        {/* 斜体按钮 */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              disabled={!isNodeSelected}
              onClick={() => selectedNode && toggleItalic(selectedNode.id)}
              className={`button-square ${currentNode?.style.fontStyle === "italic" ? "button-active" : ""}`}
            >
              <Italic className="icon-small" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>斜体（Crtl+I）</TooltipContent>
        </Tooltip>

        {/* 字体颜色 */}
        <Popover
          open={colorPickerOpen}
          onOpenChange={(open) => {
            setColorPickerOpen(open);
          }}
        >
          <Tooltip>
            <TooltipTrigger
              asChild
              onFocus={(e) => {
                // 阻止焦点触发 tooltip
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <PopoverTrigger asChild>
                <Button
                  ref={colorButtonRef} // 使用本地 ref
                  variant="ghost"
                  size="sm"
                  disabled={!isNodeSelected}
                  className={`button-square ${colorPickerOpen ? "button-active" : ""}`}
                >
                  <svg
                    className="icon icon-small"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                  >
                    <path
                      d="M825.6 652.8L544 83.2C537.6 70.4 524.8 64 512 64s-25.6 6.4-32 19.2l-281.6 569.6c-6.4 19.2 0 38.4 19.2 51.2 19.2 6.4 38.4 0 51.2-19.2L384 454.4h275.2l115.2 230.4c6.4 19.2 32 25.6 51.2 19.2 6.4-12.8 12.8-32 0-51.2zM409.6 384L512 172.8 614.4 384H409.6z"
                      fill="#2c2c2c"
                      p-id="9382"
                    ></path>
                    <path
                      d="M876.8 960H147.2c-44.8 0-83.2-38.4-83.2-83.2v-19.2c0-51.2 38.4-89.6 83.2-89.6h723.2c44.8 0 83.2 38.4 83.2 83.2v19.2c6.4 51.2-32 89.6-76.8 89.6z"
                      p-id="9383"
                      data-spm-anchor-id="a313x.search_index.0.i19.4e3e3a81nzKYDC"
                      fill={currentNode?.style.color || "#000000"}
                    ></path>
                  </svg>{" "}
                  <div className="margin-left-medium">
                    {colorPickerOpen ? (
                      <ChevronUp className="dropdown-arrow" />
                    ) : (
                      <ChevronDown className="dropdown-arrow" />
                    )}
                  </div>
                </Button>
              </PopoverTrigger>
            </TooltipTrigger>
            <TooltipContent>字体颜色</TooltipContent>
          </Tooltip>
          <PopoverContent className="popover-content-color">
            <CustomColorPicker
              color={currentNode?.style.color || "#000000"}
              onChange={(color) => {
                if (selectedNode) {
                  updateTextColor(selectedNode.id, color);
                }
              }}
              onComplete={() => {
                setColorPickerOpen(false);
              }}
            />
          </PopoverContent>
        </Popover>
        <Separator orientation="vertical" className="separator" />

        {start_b_icons.map(
          ({ icon, label, onClick, showDropdownArrow, iconOnly }, index) => (
            <React.Fragment key={label}>
              {/* 如果前一个图标有 addSeparator 属性，则在当前图标前显示分隔符 */}
              {index > 0 && start_b_icons[index - 1].addSeparator && (
                <Separator orientation="vertical" className="separator" />
              )}
              <div className="button-container">
                <TooltipButton
                  icon={icon ? <img src={icon} alt={label} /> : null} // 只有当icon不为空时才显示图标
                  label={label} // 文本
                  onClick={onClick} // 传入按钮点击时调用的函数
                  disabled={!isNodeSelected} // 如果没有选中节点，按钮禁用
                  showDropdownArrow={showDropdownArrow} // 是否显示下拉箭头
                  iconOnly={iconOnly} // 是否只显示图标
                />
              </div>
            </React.Fragment>
          )
        )}
      </TooltipProvider>
    </div>
  );
};
