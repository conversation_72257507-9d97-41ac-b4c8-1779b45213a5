import { useCallback, useRef } from "react";
import type { MindMapData, MindMapNode as NodeType } from "../types/mindmap";

interface NodeDimensions {
  width: number;
  height: number;
}

interface UseChildNodeAdjustmentProps {
  mindMapData: MindMapData;
  nodeDimensions: Record<string, NodeDimensions>;
  onNodeUpdate: (nodeId: string, updates: Partial<NodeType>) => void;
}

interface UseChildNodeAdjustmentReturn {
  handleNodeDimensionUpdate: (
    nodeId: string,
    dimensions: NodeDimensions,
    isFromEditing?: boolean
  ) => void;
  handleAddChildWithDimensions: (
    parentId: string,
    onAddChild: (parentId: string) => void
  ) => void;
  adjustNewChildPosition: (parentId: string) => void;
}

export function useChildNodeAdjustment({
  mindMapData,
  nodeDimensions,
  onNodeUpdate,
}: UseChildNodeAdjustmentProps): UseChildNodeAdjustmentReturn {
  // 防止无限循环的标志
  const isAdjustingPositions = useRef(false);

  // 调整子节点位置（在曲线布局基础上进行微调）
  const adjustChildrenPositions = useCallback(
    (parentId: string, oldWidth?: number, newWidth?: number) => {
      if (isAdjustingPositions.current) return;

      const parent = mindMapData.nodes[parentId];
      if (!parent || parent.children.length === 0) return;

      // 如果提供了宽度参数，计算宽度差异
      let widthDiff = 0;
      if (oldWidth !== undefined && newWidth !== undefined) {
        widthDiff = newWidth - oldWidth;
        if (Math.abs(widthDiff) < 1) return; // 忽略微小的变化
      }

      isAdjustingPositions.current = true;

      // 获取父节点的实际尺寸
      const parentDimensions = nodeDimensions[parentId];
      if (!parentDimensions) {
        isAdjustingPositions.current = false;
        return;
      }

      // 递归调整所有后代节点的X位置
      const adjustAllDescendants = (
        nodeId: string,
        currentWidthDiff: number
      ) => {
        const node = mindMapData.nodes[nodeId];
        if (!node) return;

        node.children.forEach((childId) => {
          const child = mindMapData.nodes[childId];
          if (child) {
            // 计算子节点应该紧贴父节点右边框的新X位置
            const newX = node.position.x + (nodeDimensions[nodeId]?.width || 0);

            // 如果有宽度变化，在新位置基础上应用宽度差异
            const finalX =
              currentWidthDiff !== 0 ? newX + currentWidthDiff : newX;

            onNodeUpdate(childId, {
              position: {
                ...child.position,
                x: finalX,
              },
            });

            // 递归调整子节点的子节点
            adjustAllDescendants(childId, currentWidthDiff);
          }
        });
      };

      adjustAllDescendants(parentId, widthDiff);

      // 延迟重置标志，避免阻塞后续的正常更新
      setTimeout(() => {
        isAdjustingPositions.current = false;
      }, 100);
    },
    [mindMapData.nodes, onNodeUpdate, nodeDimensions]
  );

  // 处理节点尺寸更新
  const handleNodeDimensionUpdate = useCallback(
    (
      nodeId: string,
      dimensions: NodeDimensions,
      isFromEditing: boolean = false
    ) => {
      const prevDimensions = nodeDimensions[nodeId];

      // 只有在编辑模式下且节点宽度发生变化时，才调整子节点位置
      if (
        !isAdjustingPositions.current &&
        isFromEditing &&
        prevDimensions &&
        Math.abs(prevDimensions.width - dimensions.width) > 1
      ) {
        const node = mindMapData.nodes[nodeId];
        if (node && node.children.length > 0) {
          // 使用 setTimeout 确保状态更新完成后再调整位置
          setTimeout(() => {
            adjustChildrenPositions(
              nodeId,
              prevDimensions.width,
              dimensions.width
            );
          }, 0);
        }
      }
    },
    [mindMapData.nodes, adjustChildrenPositions, nodeDimensions]
  );

  // 处理添加子节点（添加后立即调整位置以保持对齐）
  const handleAddChildWithDimensions = useCallback(
    (parentId: string, onAddChild: (parentId: string) => void) => {
      // 添加子节点
      onAddChild(parentId);

      // 延迟调整新子节点的位置，确保与已有子节点对齐
      setTimeout(() => {
        adjustChildrenPositions(parentId);
      }, 50); // 短暂延迟，确保节点已经添加到DOM中
    },
    [adjustChildrenPositions]
  );

  // 调整新添加子节点的位置（基于父节点实际宽度）
  const adjustNewChildPosition = useCallback(
    (parentId: string) => {
      // 直接调用 adjustChildrenPositions 来处理位置调整
      // 不传递宽度参数，让它根据当前实际尺寸重新计算位置
      adjustChildrenPositions(parentId);
    },
    [adjustChildrenPositions]
  );

  return {
    handleNodeDimensionUpdate,
    handleAddChildWithDimensions,
    adjustNewChildPosition,
  };
}
