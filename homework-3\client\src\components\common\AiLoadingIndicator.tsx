import React from "react";
import "./AiLoadingIndicator.css";

interface AiLoadingIndicatorProps {
  isVisible: boolean;
  progress?: string;
}

const AiLoadingIndicator: React.FC<AiLoadingIndicatorProps> = ({
  isVisible,
  progress,
}) => {
  if (!isVisible) return null;

  return (
    <div className="ai-loading-indicator">
      <div className="ai-loading-content">
        <div className="ai-loading-spinner">
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
        </div>
        <div className="ai-loading-text">
          <div className="ai-loading-title">🤖 AI正在创作中...</div>
          {progress && <div className="ai-loading-progress">{progress}</div>}
        </div>
      </div>
    </div>
  );
};

export default AiLoadingIndicator;
