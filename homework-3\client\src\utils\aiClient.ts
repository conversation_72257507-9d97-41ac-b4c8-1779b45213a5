/**
 * AI客户端工具类
 * 用于调用服务器的AI接口
 */

export interface AIStreamResponse {
  text?: string;
  message?: string;
  error?: string;
}

export interface AIStreamCallbacks {
  onStart?: () => void;
  onData?: (text: string) => void;
  onEnd?: (fullResponse: string) => void;
  onError?: (error: string) => void;
}

export interface AIMindMapNode {
  id: string;
  text: string;
  level: number;
  parentId: string | null;
}

export interface AIMindMapCallbacks {
  onStart?: () => void;
  onNodeGenerated?: (node: AIMindMapNode) => void;
  onEnd?: (allNodes: AIMindMapNode[]) => void;
  onError?: (error: string) => void;
}


/**
 * 调用AI流式接口
 * @param prompt 用户输入的提示词
 * @param callbacks 回调函数
 * @returns Promise<string> 完整的AI响应
 */
export const generateAi = async (
  prompt: string,
  callbacks?: AIStreamCallbacks
): Promise<string> => {
  console.log("开始调用AI生成接口...", { prompt });
  
  if (!prompt || prompt.trim() === '') {
    throw new Error('prompt不能为空');
  }

  try {
    // 调用流式AI接口
    const response = await fetch('/api/chatStream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt: prompt.trim() }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 检查响应是否为SSE格式
    if (!response.body) {
      throw new Error('Response body is null');
    }

    console.log("开始接收SSE流式响应...");
    
    // 创建流读取器
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    // 读取流数据
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log("流式响应结束");
        break;
      }

      // 解码数据
      buffer += decoder.decode(value, { stream: true });
      
      // 按行分割SSE数据
      const lines = buffer.split('\n\n');
      buffer = lines.pop() || '';
      
      // 处理每一行SSE数据
      for (const line of lines) {
        if (line.trim() === '') continue;
        
        // 解析SSE事件
        const eventMatch = line.match(/^event: (.+)$/m);
        const dataMatch = line.match(/^data: (.+)$/m);
        
        if (eventMatch && dataMatch) {
          const eventType = eventMatch[1];
          const eventData = dataMatch[1];
          
          console.log(`事件类型: ${eventType}, 数据:`, eventData);
          
          switch (eventType) {
            case 'start':
              console.log("AI开始生成响应...");
              callbacks?.onStart?.();
              break;
              
            case 'end':
              console.log("AI响应完成");
              console.log("完整响应内容:", fullResponse);
              callbacks?.onEnd?.(fullResponse);
              break;
              
            case 'error':
              console.error("AI响应错误:", eventData);
              let errorMessage = 'AI响应错误';
              try {
                const parsedError = JSON.parse(eventData);
                errorMessage = parsedError.message || parsedError.error || errorMessage;
              } catch (e) {
                errorMessage = eventData || errorMessage;
              }
              callbacks?.onError?.(errorMessage);
              break;
          }
        } else if (dataMatch) {
          // 处理没有event标签的数据行
          try {
            const parsedData: AIStreamResponse = JSON.parse(dataMatch[1]);
            if (parsedData.text) {
              console.log("接收到文本片段:", parsedData.text);
              fullResponse += parsedData.text;
              callbacks?.onData?.(parsedData.text);
            }
          } catch (e) {
            console.warn('解析数据错误:', e, dataMatch[1]);
          }
        }
      }
    }
    
    console.log("\n=== AI生成完成 ===");
    console.log("最终完整响应:", fullResponse);
    
    return fullResponse;
    
  } catch (error) {
    console.error('调用AI接口时发生错误:', error);
    
    // 如果是网络错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('fetch')) {
      const networkError = '网络连接错误，请检查服务器是否正在运行';
      console.error(networkError);
      callbacks?.onError?.(networkError);
      throw new Error(networkError);
    }
    
    callbacks?.onError?.(error instanceof Error ? error.message : '未知错误');
    throw error;
  }
};



/**
 * 专门用于思维导图节点生成的AI函数
 * 解析AI返回的结构化节点数据，并流式添加到思维导图中
 * @param prompt 用户输入的提示词
 * @param parentNodeId 父节点ID，新生成的节点将作为其子节点
 * @param callbacks 回调函数
 * @returns Promise<AIMindMapNode[]> 生成的所有节点
 */
export const generateAiMindMapNodes = async (
  prompt: string,
  parentNodeId: string,
  callbacks?: AIMindMapCallbacks
): Promise<AIMindMapNode[]> => {
  console.log("🧠 开始AI思维导图节点生成...", { prompt, parentNodeId });

  if (!prompt || prompt.trim() === '') {
    throw new Error('prompt不能为空');
  }

  const generatedNodes: AIMindMapNode[] = [];
  let buffer = '';

  // ID映射表：AI返回的逻辑ID -> 实际的节点ID
  const idMapping: { [aiId: string]: string } = {
    '1': parentNodeId, // AI返回的"1"映射到实际的父节点ID
  };

  try {
    // 调用AI接口，使用流式处理
    await generateAi(prompt, {
      onStart: () => {
        console.log("🤖 AI开始生成思维导图节点...");
        callbacks?.onStart?.();
      },

      onData: (text) => {
        console.log("📝 接收到数据片段:", text);
        buffer += text;

        // 使用STOP作为分隔符来分割完整的JSON对象
        const parts = buffer.split('STOP');
        buffer = parts.pop() || ''; // 保留最后一部分（可能不完整）

        for (const part of parts) {
          const trimmedPart = part.trim();
          if (trimmedPart === '' || trimmedPart === 'END') {
            continue;
          }

          try {
            // 尝试解析JSON节点数据
            const nodeData = JSON.parse(trimmedPart);

            if (nodeData.id && nodeData.text && nodeData.level) {
              // 跳过level 1节点（根节点）
              if (nodeData.level === 1) {
                console.log("⏭️ 跳过level 1节点:", nodeData);
                continue;
              }

              // 映射AI返回的parentId到实际的节点ID
              let actualParentId = nodeData.parentId || parentNodeId;
              if (nodeData.parentId && idMapping[nodeData.parentId]) {
                actualParentId = idMapping[nodeData.parentId];
                console.log(`🔗 ID映射: AI的"${nodeData.parentId}" -> 实际的"${actualParentId}"`);
              }

              // 生成实际的节点ID（使用时间戳和随机数）
              const actualNodeId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

              // 将AI的ID映射到实际的节点ID，供后续节点使用
              idMapping[nodeData.id] = actualNodeId;

              // 构造思维导图节点
              const mindMapNode: AIMindMapNode = {
                id: actualNodeId,
                text: nodeData.text,
                level: nodeData.level,
                parentId: actualParentId,
              };

              console.log("✅ 解析到完整节点:", mindMapNode);
              console.log("🗂️ 当前ID映射表:", idMapping);
              generatedNodes.push(mindMapNode);

              // 立即回调，实现流式渲染
              callbacks?.onNodeGenerated?.(mindMapNode);
            }
          } catch (e) {
            // 解析失败，可能是不完整的JSON，继续等待更多数据
            console.log("⏳ JSON解析失败，等待更多数据:", trimmedPart);
          }
        }
      },

      onEnd: (_fullResponse) => {
        console.log("🎉 AI思维导图节点生成完成");
        console.log("📊 生成的节点数量:", generatedNodes.length);
        console.log("📋 所有节点:", generatedNodes);

        // 处理buffer中剩余的数据
        if (buffer.trim()) {
          const remainingData = buffer.trim().replace(/END$/, '').trim();
          if (remainingData) {
            try {
              const nodeData = JSON.parse(remainingData);
              if (nodeData.id && nodeData.text && nodeData.level && nodeData.level !== 1) {
                // 映射AI返回的parentId到实际的节点ID
                let actualParentId = nodeData.parentId || parentNodeId;
                if (nodeData.parentId && idMapping[nodeData.parentId]) {
                  actualParentId = idMapping[nodeData.parentId];
                }

                // 生成实际的节点ID
                const actualNodeId = `ai-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

                const mindMapNode: AIMindMapNode = {
                  id: actualNodeId,
                  text: nodeData.text,
                  level: nodeData.level,
                  parentId: actualParentId,
                };

                console.log("✅ 解析到最后一个节点:", mindMapNode);
                generatedNodes.push(mindMapNode);
                callbacks?.onNodeGenerated?.(mindMapNode);
              }
            } catch (e) {
              console.warn("⚠️ 最后一段数据解析失败:", remainingData);
            }
          }
        }

        callbacks?.onEnd?.(generatedNodes);
      },

      onError: (error) => {
        console.error("❌ AI思维导图节点生成错误:", error);
        callbacks?.onError?.(error);
      }
    });

    return generatedNodes;

  } catch (error) {
    console.error('💥 AI思维导图节点生成失败:', error);
    callbacks?.onError?.(error instanceof Error ? error.message : '未知错误');
    throw error;
  }
};

// 导出默认的generateAi函数
export default generateAi;

