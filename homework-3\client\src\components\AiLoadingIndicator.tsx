import React from "react";
import "./AiLoadingIndicator.css";

interface AiLoadingIndicatorProps {
  isVisible: boolean;
}

const AiLoadingIndicator: React.FC<AiLoadingIndicatorProps> = ({
  isVisible,
}) => {
  if (!isVisible) return null;

  return (
    <div className="ai-loading-indicator">
      <div className="ai-loading-content">
        <div className="ai-loading-spinner">
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
          <div className="spinner-ring"></div>
        </div>
        <div className="ai-loading-text">
          <div className="ai-loading-title">🤖 AI正在创作中...</div>
        </div>
      </div>
    </div>
  );
};

export default AiLoadingIndicator;
